# Barcode Tracker Application - Deployment Guide

## Overview

The Barcode Tracker Application is a Java-based system that manages associate sessions and tote scanning operations. It consists of a backend Java application with Redis for caching and PostgreSQL for persistent storage, plus a React frontend for data visualization.

## Architecture Components

- **Backend**: Java application with a Spring Boot API application
- **Database**: PostgreSQL
- **Cache**: Redis
- **Frontend**: React.js with Recharts
- **Build Tool**: Maven

## Prerequisites

### System Requirements
- Java 11 or higher
- Maven 3.6+
- PostgreSQL 12+
- Redis 6+
- Node.js 16+ (for frontend)
- npm or yarn

### Hardware Requirements
- **Minimum**: 4GB RAM, 2 CPU cores, 20GB disk space
- **Recommended**: 8GB RAM, 4 CPU cores, 50GB disk space

## Installation Steps

### 1. Database Setup (PostgreSQL)

#### Install PostgreSQL
```bash
# Ubuntu/Debian
sudo apt update
sudo apt install postgresql postgresql-contrib

# CentOS/RHEL
sudo yum install postgresql-server postgresql-contrib
sudo postgresql-setup initdb
sudo systemctl start postgresql
sudo systemctl enable postgresql
```

#### Create Database and User
```sql
-- Connect as postgres user
sudo -u postgres psql

-- Create database
CREATE DATABASE kbsdb;

-- Create user
CREATE USER postgres WITH PASSWORD 'admin@123'; --please change this password after initial build and store in a pw vault

-- Grant privileges
GRANT ALL PRIVILEGES ON DATABASE kbsdb TO postgres;

-- Exit
\q
```

#### Create Database Tables
```sql
-- Connect to kbsdb
psql -U postgres -d kbsdb

-- Associate Details Table
CREATE TABLE associatedetail (
    EUID VARCHAR(50) PRIMARY KEY,
    EUFullName VARCHAR(255),
    IsActive BOOLEAN DEFAULT true,
    CreatedDate TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Associate Session Summary Table
CREATE TABLE AssociateSessionSummary (
    ID SERIAL PRIMARY KEY,
    EUID VARCHAR(50) NOT NULL,
    ScannerID VARCHAR(50) NOT NULL,
    SessionID BIGINT NOT NULL,
    SessionStartTime TIMESTAMP NOT NULL,
    SessionEndTime TIMESTAMP NOT NULL,
    ToteCount INTEGER DEFAULT 0,
    FCZoneStationScannerMapid INTEGER,
    CreatedDate TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Associate Session Details Table
CREATE TABLE AssociateSessionDetail (
    ID SERIAL PRIMARY KEY,
    SessionID BIGINT NOT NULL,
    toteID VARCHAR(50) NOT NULL,
    toteScanTime TIMESTAMP NOT NULL,
    CreatedDate TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Stations Table
CREATE TABLE stations (
    ID SERIAL PRIMARY KEY,
    ScannerMAC VARCHAR(50) UNIQUE NOT NULL,
    StationName VARCHAR(100),
    IsActive BOOLEAN DEFAULT true,
    CreatedDate TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- FC Zone Station Scanner Map Table
CREATE TABLE FCZoneStationScannerMap (
    ID SERIAL PRIMARY KEY,
    ScannerID VARCHAR(50) NOT NULL,
    ZoneName VARCHAR(100),
    StationName VARCHAR(100),
    IsActive BOOLEAN DEFAULT true,
    CreatedDate TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create function for adding associates
CREATE OR REPLACE FUNCTION add_associate_if_not_exists(p_euid VARCHAR)
RETURNS VOID AS $$
BEGIN
    INSERT INTO associatedetail (EUID, EUFullName, IsActive)
    VALUES (p_euid, p_euid, true)
    ON CONFLICT (EUID) DO NOTHING;
END;
$$ LANGUAGE plpgsql;
```

### 2. Redis Setup

#### Install Redis
```bash
# Ubuntu/Debian
sudo apt install redis-server

# CentOS/RHEL
sudo yum install redis
sudo systemctl start redis
sudo systemctl enable redis

# Verify installation
redis-cli ping
# Should return: PONG
```

#### Configure Redis (Optional)
```bash
# Edit Redis configuration
sudo nano /etc/redis/redis.conf

# Key settings to verify:
# bind 127.0.0.1
# port 6379
# maxmemory-policy allkeys-lru

# Restart Redis
sudo systemctl restart redis
```

### 3. Application Configuration

#### Update Configuration File
Edit `src/main/resources/config.properties`:
```properties
# Database Configuration
app.DbURL=**************************************
app.DbUserName=postgres
app.DbPassword=admin@123

# Redis Configuration
app.RedisConnectionString=localhost
app.RedisConnectionPort=6379

# Session Configuration
app.sessiontimeout=300
```

### 4. Backend Deployment

#### Build the Application
```bash
# Navigate to project root
cd /path/to/barcodetracker

# Clean and compile
mvn clean compile

# Run tests (optional)
mvn test

# Package the application
mvn package

# The JAR file will be created in target/ directory
```

#### Run the Application
```bash
# Method 1: Direct execution
java -jar target/barcodetracker-1.0.jar

# Method 2: Using Maven
mvn exec:java -Dexec.mainClass="com.kbs.MultiScanner"

# Method 3: Run specific components
# For data archival
java -cp target/classes:target/lib/* com.kbs.ArchiveData

# For associate processing
java -cp target/classes:target/lib/* com.kbs.ProcessAssociates
```

### 5. Frontend Deployment

#### Install Dependencies
```bash
# Navigate to frontend directory
cd path/to/adminapp
&&
cd path/to/scoreboardapp


# Install dependencies
npm install
```

#### Build Frontend
```bash
# Development build
npm run dev

# Production build
npm run build
```

#### Serve Frontend
```bash
# Development server
npm start

# Production server (after build)
# Serve the build directory with a web server like nginx or apache
```

## Production Deployment

### 1. System Service Setup

#### Create Systemd Service
```bash
# Create service file
sudo nano /etc/systemd/system/barcodetracker.service
```

```ini
[Unit]
Description=Barcode Tracker Application
After=network.target postgresql.service redis.service

[Service]
Type=simple
User=barcodetracker
WorkingDirectory=/opt/barcodetracker
ExecStart=/usr/bin/java -jar /opt/barcodetracker/barcodetracker.jar
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
```

#### Enable and Start Service
```bash
# Reload systemd
sudo systemctl daemon-reload

# Enable service
sudo systemctl enable barcodetracker

# Start service
sudo systemctl start barcodetracker

# Check status
sudo systemctl status barcodetracker
```

### 2. Web Server Configuration (Nginx) - Not configured yet but will be for pilot/production

#### Install Nginx
```bash
sudo apt install nginx
```

#### Configure Nginx
```bash
sudo nano /etc/nginx/sites-available/barcodetracker
```

```nginx
server {
    listen 80;
    server_name kroger-kbs.com;

    # Frontend static files
    location / {
        root /opt/barcodetracker/frontend/build;
        try_files $uri $uri/ /index.html;
    }

    # Backend API
    location /api/ {
        proxy_pass http://localhost:8080;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

#### Enable Site
```bash
sudo ln -s /etc/nginx/sites-available/barcodetracker /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl restart nginx
```

## Configuration Management

### Environment Variables
```bash
# Set environment variables
export DB_URL="**************************************"
export DB_USERNAME="postgres"
export DB_PASSWORD="admin@123" ###please change this password after initial build and store in a pw vault
export REDIS_HOST="localhost"
export REDIS_PORT="6379"
export SESSION_TIMEOUT="300"
```

### Application Properties
Key configuration parameters in `config.properties`:

| Property | Description | Default |
|----------|-------------|---------|
| `app.sessiontimeout` | Session timeout in seconds | 300 |
| `app.DbURL` | PostgreSQL connection URL | ************************************** |
| `app.DbUserName` | Database username | postgres |
| `app.DbPassword` | Database password | admin@123 | ###please change this password after initial build and store in a pw vault
| `app.RedisConnectionString` | Redis host | localhost |
| `app.RedisConnectionPort` | Redis port | 6379 |

## Monitoring and Maintenance

### Log Files
- Application logs: Check console output or configure logging framework
- PostgreSQL logs: `/var/log/postgresql/`
- Redis logs: `/var/log/redis/`
- Nginx logs: `/var/log/nginx/`

### Health Checks
```bash
# Check database connection
psql -U postgres -d kbsdb -c "SELECT 1;"

# Check Redis connection
redis-cli ping

# Check application status
curl http://localhost:8080/api/hourly-barcodes?key=test

# Check system resources
htop
df -h
```

### Backup Procedures
```bash
# Database backup
pg_dump -U postgres -d kbsdb > backup_$(date +%Y%m%d_%H%M%S).sql

# Redis backup
redis-cli BGSAVE

# Application backup
tar -czf app_backup_$(date +%Y%m%d_%H%M%S).tar.gz /opt/barcodetracker/
```

## Troubleshooting

### Common Issues

#### Database Connection Issues
```bash
# Check PostgreSQL status
sudo systemctl status postgresql

# Check database connectivity
psql -U postgres -d kbsdb

# Verify configuration
cat src/main/resources/config.properties | grep -i db
```

#### Redis Connection Issues
```bash
# Check Redis status
sudo systemctl status redis

# Test Redis connection
redis-cli ping

# Check Redis configuration
redis-cli CONFIG GET "*"
```

#### Application Startup Issues
```bash
# Check Java version
java -version

# Verify classpath
java -cp target/classes:target/lib/* -version

# Check for port conflicts
netstat -tulpn | grep :8080
```

### Performance Tuning

#### Database Optimization
```sql
-- Create indexes for better performance
CREATE INDEX idx_associate_session_summary_euid ON AssociateSessionSummary(EUID);
CREATE INDEX idx_associate_session_summary_scanner ON AssociateSessionSummary(ScannerID);
CREATE INDEX idx_associate_session_summary_time ON AssociateSessionSummary(SessionStartTime);
```

#### Redis Optimization
```bash
# Monitor Redis memory usage
redis-cli INFO memory

# Set memory policy
redis-cli CONFIG SET maxmemory-policy allkeys-lru
```

## Docker Containerization

### Prerequisites for Docker Deployment
- Docker 20.10+
- Docker Compose 2.0+

### 1. Create Dockerfiles

#### Backend Dockerfile
Create `Dockerfile` in project root:
```dockerfile
FROM openjdk:11-jre-slim

# Set working directory
WORKDIR /app

# Maven dependencies
COPY target/lib/ /app/lib/
COPY target/classes/ /app/classes/

# Copy application JAR
COPY target/*.jar /app/app.jar

# Create non-root user
RUN groupadd -r appuser && useradd -r -g appuser appuser
RUN chown -R appuser:appuser /app
USER appuser

# Expose port
EXPOSE 8080

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:8080/health || exit 1

# Run application
ENTRYPOINT ["java", "-cp", "/app/classes:/app/lib/*", "com.kbs.MultiScanner"]
```

#### Frontend Dockerfile
Create `frontend/Dockerfile`:
```dockerfile
# Multi-stage build
FROM node:16-alpine AS builder

WORKDIR /app
COPY src/main/js/package*.json ./
RUN npm ci --only=production

COPY src/main/js/ ./
RUN npm run build

# Production stage
FROM nginx:alpine

# Copy built assets
COPY --from=builder /app/build /usr/share/nginx/html

# Copy nginx configuration
COPY nginx.conf /etc/nginx/conf.d/default.conf

# Expose port
EXPOSE 80

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost/ || exit 1

CMD ["nginx", "-g", "daemon off;"]
```

### 2. Docker Compose Configuration

Create `docker-compose.yml`:
```yaml
version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:13-alpine
    container_name: barcodetracker-db
    environment:
      POSTGRES_DB: kbsdb
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: admin@123 #please change after creation and store in a pw vault
      PGDATA: /var/lib/postgresql/data/pgdata
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init-db.sql:/docker-entrypoint-initdb.d/init-db.sql:ro
    ports:
      - "5432:5432"
    networks:
      - barcodetracker-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d kbsdb"]
      interval: 30s
      timeout: 10s
      retries: 3
    restart: unless-stopped

  # Redis Cache
  redis:
    image: redis:6-alpine
    container_name: barcodetracker-redis
    command: redis-server --appendonly yes --maxmemory 256mb --maxmemory-policy allkeys-lru
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    networks:
      - barcodetracker-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
    restart: unless-stopped

  # Backend Application
  backend:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: barcodetracker-backend
    environment:
      - DB_URL=*************************************
      - DB_USERNAME=postgres
      - DB_PASSWORD=admin@123 ###please change this password after initial build and store in a pw vault
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - SESSION_TIMEOUT=300
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    ports:
      - "8080:8080"
    networks:
      - barcodetracker-network
    volumes:
      - app_logs:/app/logs
    restart: unless-stopped

  # Frontend Application
  frontend:
    build:
      context: .
      dockerfile: frontend/Dockerfile
    container_name: barcodetracker-frontend
    depends_on:
      - backend
    ports:
      - "80:80"
    networks:
      - barcodetracker-network
    restart: unless-stopped

  # Archive Data Service
  archive-service:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: barcodetracker-archive
    environment:
      - DB_URL=*************************************
      - DB_USERNAME=postgres
      - DB_PASSWORD=admin@123 ###please change this password after initial build and store in a pw vault
      - REDIS_HOST=redis
      - REDIS_PORT=6379
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - barcodetracker-network
    entrypoint: ["sh", "-c", "while true; do java -cp /app/classes:/app/lib/* com.kbs.ArchiveData; sleep 300; done"]
    restart: unless-stopped

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  app_logs:
    driver: local

networks:
  barcodetracker-network:
    driver: bridge
```

### 3. Additional Configuration Files

#### Database Initialization Script
Create `init-db.sql`:
```sql
-- Create tables and functions
CREATE TABLE IF NOT EXISTS associatedetail (
    EUID VARCHAR(50) PRIMARY KEY,
    EUFullName VARCHAR(255),
    IsActive BOOLEAN DEFAULT true,
    CreatedDate TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS AssociateSessionSummary (
    ID SERIAL PRIMARY KEY,
    EUID VARCHAR(50) NOT NULL,
    ScannerID VARCHAR(50) NOT NULL,
    SessionID BIGINT NOT NULL,
    SessionStartTime TIMESTAMP NOT NULL,
    SessionEndTime TIMESTAMP NOT NULL,
    ToteCount INTEGER DEFAULT 0,
    FCZoneStationScannerMapid INTEGER,
    CreatedDate TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS AssociateSessionDetail (
    ID SERIAL PRIMARY KEY,
    SessionID BIGINT NOT NULL,
    toteID VARCHAR(50) NOT NULL,
    toteScanTime TIMESTAMP NOT NULL,
    CreatedDate TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS stations (
    ID SERIAL PRIMARY KEY,
    ScannerMAC VARCHAR(50) UNIQUE NOT NULL,
    StationName VARCHAR(100),
    IsActive BOOLEAN DEFAULT true,
    CreatedDate TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS FCZoneStationScannerMap (
    ID SERIAL PRIMARY KEY,
    ScannerID VARCHAR(50) NOT NULL,
    ZoneName VARCHAR(100),
    StationName VARCHAR(100),
    IsActive BOOLEAN DEFAULT true,
    CreatedDate TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create function
CREATE OR REPLACE FUNCTION add_associate_if_not_exists(p_euid VARCHAR)
RETURNS VOID AS $$
BEGIN
    INSERT INTO associatedetail (EUID, EUFullName, IsActive)
    VALUES (p_euid, p_euid, true)
    ON CONFLICT (EUID) DO NOTHING;
END;
$$ LANGUAGE plpgsql;

-- Sample data
INSERT INTO stations (ScannerMAC, StationName) VALUES
('SC00001', 'Station 1'),
('SC00002', 'Station 2'),
('SC00003', 'Station 3'),
('SC00004', 'Station 4'),
('SC00005', 'Station 5')
ON CONFLICT (ScannerMAC) DO NOTHING;
```

#### Nginx Configuration
Create `nginx.conf`:
```nginx
server {
    listen 80;
    server_name localhost;

    # Frontend static files
    location / {
        root /usr/share/nginx/html;
        index index.html index.htm;
        try_files $uri $uri/ /index.html;
    }

    # Backend API proxy
    location /api/ {
        proxy_pass http://localhost:8080;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
    }

    # Health check endpoint
    location /health {
        access_log off;
        return 200 "healthy\n";
        add_header Content-Type text/plain;
    }
}
```

### 4. Docker Deployment Commands - We can do straight docker or use compose

```bash
docker build -t barcodetracker .
docker run -d -p 8080:8080 barcodetracker

docker build -t barcodeapi .
docker run -d -p 8081:8080 performanceapi

docker build -t adminapp .
docker run -d -p 3000:3000

docker build -t scoreboardapp .
docker run -d -p 3001:3000 scoreboardapp
```

### Above commands will run the services in the background if we want to restart on shutdown, paused, or failure include the following:
```bash
docker run -d --restart unless-stopped -p 8080:8080 barcodetracker

docker run -d --restart unless-stopped -p 8081:8080 performanceapi

docker run -d --restart unless-stopped -p 3000:3000 adminapp

docker run -d --restart unless-stopped -p 3001:3000 scoreboardapp
```

#### Build and Start Services
```bash
# Build all services
docker-compose build

# Start all services
docker-compose up -d

# View logs
docker-compose logs -f

# Check service status
docker-compose ps
```

#### Individual Service Management
```bash
# Start specific service
docker-compose up -d postgres redis

# Restart service
docker-compose restart backend

# Stop all services
docker-compose down

# Stop and remove volumes
docker-compose down -v
```

### 5. Production Docker Deployment

#### Environment-specific Compose Files
Create `docker-compose.prod.yml`:
```yaml
version: '3.8'

services:
  postgres:
    environment:
      POSTGRES_PASSWORD_FILE: /run/secrets/db_password
    secrets:
      - db_password
    deploy:
      resources:
        limits:
          memory: 1G
        reservations:
          memory: 512M

  redis:
    deploy:
      resources:
        limits:
          memory: 512M
        reservations:
          memory: 256M

  backend:
    environment:
      - JAVA_OPTS=-Xmx1g -Xms512m
    deploy:
      replicas: 2
      resources:
        limits:
          memory: 1.5G
        reservations:
          memory: 1G

  frontend:
    deploy:
      replicas: 2

secrets:
  db_password:
    file: ./secrets/db_password.txt
```

#### Production Deployment
```bash
# Deploy with production overrides
docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d

# Scale services
docker-compose up -d --scale backend=3 --scale frontend=2
```

### 6. Docker Monitoring and Maintenance

#### Health Monitoring
```bash
# Check container health
docker-compose ps

# View container stats
docker stats

# Check logs
docker-compose logs -f backend
docker-compose logs -f postgres
```

#### Backup and Restore
```bash
# Database backup
docker-compose exec postgres pg_dump -U postgres kbsdb > backup.sql

# Database restore
docker-compose exec -T postgres psql -U postgres kbsdb < backup.sql

# Volume backup
docker run --rm -v barcodetracker_postgres_data:/data -v $(pwd):/backup alpine tar czf /backup/postgres_backup.tar.gz /data
```

#### Updates and Maintenance
```bash
# Update images
docker-compose pull

# Rebuild and restart
docker-compose up -d --build

# Clean up unused resources
docker system prune -a
```

### 7. Docker Swarm Deployment - not used right now but could be used in the future if scale becomes an issue.

#### Initialize Swarm
```bash
# Initialize swarm
docker swarm init

# Deploy stack
docker stack deploy -c docker-compose.yml barcodetracker

# List services
docker service ls

# Scale service
docker service scale barcodetracker_backend=3
```