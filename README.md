### Building and launching your container

In the app file there is a `dockerfile` and a `.dockerignore` These are the configurations for you build.

If on Windows, install [Rancher Desktop](https://rancherdesktop.io/), it should install [WSL2](https://learn.microsoft.com/en-us/windows/wsl/install). If it does not, trying installing WSL2 from powershell.

Open powershell

`wsl --install`

Ubuntu should be installed by default but check `wsl -l -v`

If not you can see available versions for download `wsl -l -o`

Install the latest LTS of Ubuntu in there is not a distro installed or you need to change it `wsl --install -d <Distribution Name>`

open wsl as an admin

cd to your app directory

turn off zscaler

`sudo docker build -t BarCodeTracker .`

if there is an issue you can try `docker builder prune`

`docker run -p 8080:8080 barcodetracker`
