package com.call.bardata;

import javax.swing.DefaultListModel;

/* 
import com.honeywell.scanneredge.CallbackConnected;
import com.honeywell.scanneredge.CallbackFoundDevice;
import com.honeywell.scanneredge.CallbackReceiveDecode;
import com.honeywell.scanneredge.CallbackReceiveImage;
import com.honeywell.scanneredge.CallbackRecevieFocusResult;
import com.honeywell.scanneredge.CallbackUpgradeFWProgress;
import com.honeywell.scanneredge.EnumDeviceCtrlType;
import com.honeywell.scanneredge.EnumIllumLedPart;
import com.honeywell.scanneredge.EnumImageFormat;
import com.honeywell.scanneredge.NwkDeviceParam;
import com.honeywell.scanneredge.SdkHelper;

*/

public class SdkOperation {
	// singleton
	private static class SingletonHolder {
		private static final SdkOperation INSTANCE = new SdkOperation();
	}

	public static SdkOperation getInstance() {
		return SingletonHolder.INSTANCE;
	}

	private DefaultListModel<String> listscanner = new DefaultListModel<>();
	// private SdkHelper pSdk = new SdkHelper();

	private SdkOperation() {
		OutputLogs.getInstance().writeLog("---sdkOperation init---");
	}

	public void cancelUpgradeFW(String SN) {
		// pSdk.cancelUpgradeFW(SN);
	}

	public void ConnectDevice(String sn) {
		registerCallbacks(sn);
		OutputLogs.getInstance().writeLog("---sdkOperation connect SN: " + sn + "---");
		// pSdk.connect(sn);
	}

	// default current device
	public void defaultCurrentDev() {
		// String title = UILayout.getInstance().getTabTitle();
		String title = "";
		if (title != null) {
			// EnumDeviceCtrlType ctrl = EnumDeviceCtrlType.eCtrlDefault;
			// int mode = ctrl.getValue();
			// pSdk.deviceCtrl(mode, title);
		}
	}

	public void DisconnDevice(String sn) {
//		registerCallbacks(sn);
		// pSdk.disconnect(sn);
	}

	private int getJobId(String SN) {
		int[] jid = new int[1];
		// int val = pSdk.getCurJobID(jid, SN);
		int jobId = jid[0];
		return jobId;
	}

	public DefaultListModel<String> getScannerList() {
		return listscanner;
	}

	public String getSdkVer() {
		// return pSdk.getVersion();
		return "0.0";
	}

	// reboot current device
//	eCtlTypUnknown,
//    eCtlTypTrigger,
//    eCtlTypUnTrigger,
//    eCtlStartUpgrade,
//    eCtlStopUpgrade,
//    eCtlReboot,
//    eCtlDefault,
	public void rebootCurrentDev() {
		// String title = UILayout.getInstance().getTabTitle();
		String title = "";
		if (title != null) {
			/* EnumDeviceCtrlType ctrl = EnumDeviceCtrlType.eCtrlReboot;
			int mode = ctrl.getValue();
			pSdk.deviceCtrl(mode, title); */
		}
	}

	// pSdk callbacks
	public void registerCallbacks(String SN) {
		OutputLogs.getInstance().writeLog("---sdkOperation register cb---");
		// Connect status change
		/* pSdk.registerCbConnectStatus(new CallbackConnected() {
			@Override
			public void invoke(boolean bConnected, String SN) {
				OutputLogs.getInstance().writeLog("---sdkOperation Connect cb: " + bConnected + ", SN: " + SN + "---");
				// UILayout.getInstance().setConnectStatus(bConnected, SN);
			}
		}, SN); */

		// get image
		/* pSdk.registerCbReceiveImage(new CallbackReceiveImage() {
			@Override
			public void invoke(byte[] image, String SN) {
				// UILayout.getInstance().setRecvImage(image, SN);
				OutputLogs.getInstance().writeLog("---get image cb: " + SN + ", byte: " + image + "---");
//				System.out.println("---get image cb: " + SN + ", byte: " + image + " ---");
			}
		}, SN); */

		// auto focus
		/* pSdk.registerCbAutoFocusResult(new CallbackRecevieFocusResult() {
			@Override
			public void invoke(int focus_pos, int quality, String SN) {
				// UILayout.getInstance().setAutofocusRet(focus_pos, SN);
			}
		}, SN); */

		// get decode
		/* pSdk.registerCbReceiveDecode(new CallbackReceiveDecode() {
			@Override
			public void invoke(String decode, String SN) {
				// UILayout.getInstance().setDecodeRet(decode, SN);
				ScannersData.getInstance().addOrUpdate(SN, decode);
				OutputLogs.getInstance().writeLog("---get decode cb: " + SN + ", decode: " + decode + "---");
				System.out.println("---get decode cb: " + SN + ", decode: " + decode + "---");
			}
		}, SN); */

		// upgrade
		/* pSdk.registerCbUpgradeFWProgress(new CallbackUpgradeFWProgress() {
			@Override
			public void invoke(int percent, String SN) {
				// UILayout.getInstance().setUpgradeProgress(percent, SN);
			}
		}, SN); */
	}

	public void searchDevices() {
		// pSdk.searchDevices();
	}

	// set decode timeout
	public void setDecodeTimeout(int time, String SN) {
		int jobId = getJobId(SN);
		// pSdk.setDecodeTimeout(jobId, time, SN);
	}

	public void setDeinitSdk() {
		// pSdk.deInit();
	}

	// set exposure mode
	public void setExpoMode(int mode, String SN) {
		int jobId = getJobId(SN);
		// pSdk.setExposureMode(jobId, 0, mode, SN);
	}

	// set exposure time
	public void setExpoTime(int time, String SN) {
		// get job id
		int jobId = getJobId(SN);
		// set expo time
		// pSdk.setExposureTime(jobId, 0, time, SN);
	}

	// set focus dist
	public void setFocusDist(int time, String SN) {
		int jobId = getJobId(SN);
		// pSdk.setFocusDistance(jobId, 0, time, SN);
	}

	public void setIlluminationByTabname(String tabname, boolean bIllumOn) {
		int jobId = getJobId(tabname);

		/* EnumIllumLedPart illum = EnumIllumLedPart.Illumination_LedPartTop;
		int topLed = illum.getValue();
		pSdk.setIllumination(jobId, 0, topLed, bIllumOn, tabname);

		illum = EnumIllumLedPart.Illumination_LedPartBottom;
		int bottomLed = illum.getValue();
		pSdk.setIllumination(jobId, 0, bottomLed, bIllumOn, tabname); */

//		//get illumination
//		boolean[] getIllumOn = new boolean[1];
//		illum = EnumIllumLedPart.Illumination_LedPartTop;
//		topLed = illum.getValue();
//    	val = pSdk.getIllumination(jobId, 0, topLed, getIllumOn, tabname);
//		ret = EnumSDKRet.values()[val];
//		System.out.println("getIllumination ledtop ret: " + ret + " bIllumOn: " + getIllumOn[0]);
//
//		illum = EnumIllumLedPart.Illumination_LedPartBottom;
//		bottomLed = illum.getValue();
//		val = pSdk.getIllumination(jobId, 0, bottomLed, getIllumOn, tabname);
//		ret = EnumSDKRet.values()[val];
//		System.out.println("getIllumination ledbottom ret: " + ret + " bIllumOn: " + getIllumOn[0]);
	}

	public void setInitSdk() {
		OutputLogs.getInstance().writeLog("---before sdk init---");
		System.out.println("---before sdk init---");
		// pSdk.init();
		OutputLogs.getInstance().writeLog("---after sdk init---");
		System.out.println("---after sdk init---");

		/* pSdk.registerCbFoundDeviceNwk(new CallbackFoundDevice() {
			@Override
			public void invoke(NwkDeviceParam deviceParam) {
				OutputLogs.getInstance().writeLog("---find dev cb---");
				System.out.println("---find dev cb---");
				setSearchDev(deviceParam);
			}
		}); */
	}

	public void setLiveViewByTabname(String tabname, boolean bLiveViewOn) {
		// int format = EnumImageFormat.IMG_JPG.ordinal();
		// pSdk.setLiveViewOn(bLiveViewOn, format, tabname);
	}

	public void setRereadDelay(int time, String SN) {
		int jobId = getJobId(SN);
		// pSdk.setReReadDelay(jobId, time, SN);
	}

	/* public void setSearchDev(NwkDeviceParam deviceParam) {
		// TODO Auto-generated method stub
		String listSN = deviceParam.mStrSN;
		String listIP = deviceParam.mIP;
		String item = listSN;
		if (!listscanner.contains(item)) {
			listscanner.addElement(item);
		}
	} */

	public void setStartFocus(String SN) {
		// pSdk.startAutoFocus(SN);
	}

	// set trigger mode
	public void setTrigMode(int mode, String SN) {
		// get job id
		int jobId = getJobId(SN);
		// set trig mode
		// ExtTriggerMode_E.ETModeOneShot.ordinal()
		// pSdk.setTriggerMode(jobId, mode, SN);
	}

	public void setTrigOn(boolean trig, String SN) {
		//pSdk.ctrlTrigger(trig, SN);
	}

	// set trig timeout
	public void setTrigTimeout(int time, String SN) {
		int jobId = getJobId(SN);
		// pSdk.setTotalTimeout(jobId, time, SN);
//		pSdk.setTriggerDelay(jobId, time, SN); : external trigger
	}

	// set work mode
	public void setWorkMode(int mode, String SN) {
		int jobId = getJobId(SN);
		// pSdk.setWorkMode(jobId, mode, SN);
	}

	public void upgradeFW(String path, String SN) {
		// pSdk.upgradeFW(path, SN);
	}
}
