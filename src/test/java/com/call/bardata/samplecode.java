package com.call.bardata;

import javax.swing.DefaultListModel;

// import com.honeywell.scanneredge.NwkDeviceParam;

public class samplecode {
	public static void main(String[] args) throws InterruptedException {
		// TODO Auto-generated method stub
		Thread.sleep(10);
		SdkOperation instance = SdkOperation.getInstance();
		instance.setInitSdk();
		Thread.sleep(10000);
		instance.searchDevices();
		Thread.sleep(10000);
		DefaultListModel<String> scannerList = instance.getScannerList();
		for (int i = 0; i < scannerList.size(); i++) {
			// Get the element at the current index
			String item = scannerList.get(i);
			instance.registerCallbacks(item);
			Thread.sleep(10000);
			instance.ConnectDevice(item);
			Thread.sleep(10000);
			// Process the item (for example, print it)
			System.out.println("Scanner item: " + item);
		}
		instance.setDeinitSdk();
		// SdkOperation.getInstance().setDeinitSdk();
		System.out.println("hello");

	}

	private DefaultListModel<String> listscanner = new DefaultListModel<>();

	/* public void setSearchDev(NwkDeviceParam deviceParam) {
		// TODO Auto-generated method stub
		String listSN = deviceParam.mStrSN;
		String listIP = deviceParam.mIP;
		String item = listSN + "(" + listIP + ")";
		if (!listscanner.contains(item)) {
			listscanner.addElement(item);
		}
	} */

}
