package com.call.bardata;

import java.util.concurrent.ConcurrentHashMap;

// this method will stored the scanner data at run time
public class ScannersData {
	// Singleton implementation
	private static class SingletonHolder {
		private static final ScannersData INSTANCE = new ScannersData();
	}

	public static ScannersData getInstance() {
		return SingletonHolder.INSTANCE;
	}

	// ConcurrentHashMap to store key-value pairs
	private final ConcurrentHashMap<String, String> store;

	private ScannersData() {
		store = new ConcurrentHashMap<>();
	}

	/**
	 * Adds a key-value pair if the key does not exist. If the key exists, updates
	 * the value.
	 *
	 * @param key   the key to add or update
	 * @param value the value associated with the key
	 */
	public void addOrUpdate(String key, String value) {
		store.put(key, value);
	}

	/**
	 * Retrieves the value associated with a key.
	 *
	 * @param key the key whose value is to be retrieved
	 * @return the value associated with the key, or null if the key does not exist
	 */
	public String get(String key) {
		return store.get(key);
	}

	/**
	 * Checks if a key exists in the store.
	 *
	 * @param key the key to check
	 * @return true if the key exists, false otherwise
	 */
	public boolean contains<PERSON>ey(String key) {
		return store.containsKey(key);
	}

	/**
	 * Removes a key-value pair from the store.
	 *
	 * @param key the key to remove
	 */
	public void remove(String key) {
		store.remove(key);
	}

	/**
	 * Returns the size of the store.
	 *
	 * @return the number of key-value pairs in the store
	 */
	public int size() {
		return store.size();
	}
}
