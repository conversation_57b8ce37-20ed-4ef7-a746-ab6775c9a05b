package com.call.bardata;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;

public class OutputLogs {
	// singleton
	private static class SingletonHolder {
		private static final OutputLogs INSTANCE = new OutputLogs();
	}

	public static OutputLogs getInstance() {
		return SingletonHolder.INSTANCE;
	}

	private File file = new File("log.txt");

	private OutputLogs() {
		try {
			file.createNewFile();
		} catch (IOException e) {
			e.printStackTrace();
		}
	}

	public void writeLog(String log) {
		try {
			FileWriter writer = new FileWriter("log.txt", true);
			writer.write(log + "\n");
			writer.close();
		} catch (IOException e) {
			e.printStackTrace();
		}
	}

}
