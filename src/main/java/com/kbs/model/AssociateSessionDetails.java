package com.kbs.model;

import java.sql.Timestamp;

public class AssociateSessionDetails {
	private String euID;
	private String scannerID;
	private Timestamp entryTime;
	private Timestamp exitTime;
	private String toteID;
	private Timestamp toteScanTime;
	private Long sessionID;

	// Getter and Setter for euID
	public String getAssociateID() {
		return euID;
	}

	public void setAssociateID(String value) {
		this.euID = value;
	}

	// Getter and Setter for entryTime
	public Timestamp getEntryTime() {
		return entryTime;
	}

	public void setEntryTime(Timestamp value) {
		this.entryTime = value;
	}

	// Getter and Setter for exitTime
	public Timestamp getExitTime() {
		return exitTime;
	}

	public void setExitTime(Timestamp value) {
		this.exitTime = value;
	}

	// Getter and Setter for toteCount
	public String getToteID() {
		return this.toteID;
	}

	public void setToteID(String toteID) {
		this.toteID = toteID;
	}

	public Timestamp gettoteScanTime() {
		return this.toteScanTime;
	}

	public void settoteScanTime(Timestamp toteScanTime) {
		this.toteScanTime = toteScanTime;
	}

	public String getScannerID() {
		return this.scannerID;
	}

	public void setScannerID(String scannerID) {
		this.scannerID = scannerID;
	}

	public Long getSessionID() {
		return this.sessionID;
	}

	public void setSessionID(Long sessionID) {
		this.sessionID = sessionID;
	}
}
