package com.kbs.model.db;

import java.sql.Timestamp;

public class AssociateSessionSummary {
	private String euID;
	private Timestamp sessionStartTime;
	private Timestamp sessionEndTime;
	private Integer toteCount;
	private Long sessionID;
	private String scannerID;
	private Integer FCZoneStationScannerMapID;

	public Integer getFCZoneStationScannerMapID() {
		return FCZoneStationScannerMapID;
	}

	public void setFCZoneStationScannerMapID(Integer FCZoneStationScannerMapID) {
		this.FCZoneStationScannerMapID = FCZoneStationScannerMapID;
	}

	public AssociateSessionSummary() {

	}

	public AssociateSessionSummary(String euID, Timestamp entryTime, Timestamp exitTime, Integer toteCount) {
		this.euID = euID;
		this.sessionStartTime = entryTime;
		this.sessionEndTime = exitTime;
		this.toteCount = toteCount;
	}

	// Getter and Setter for euID
	public String getAssociateID() {
		return euID;
	}

	public void setAssociateID(String euId) {
		this.euID = euId;
	}

	// Getter and Setter for entryTime
	public Timestamp getEntryTime() {
		return sessionStartTime;
	}

	public void setEntryTime(Timestamp entryTime) {
		this.sessionStartTime = entryTime;
	}

	// Getter and Setter for exitTime
	public Timestamp getExitTime() {
		return sessionEndTime;
	}

	public void setExitTime(Timestamp exitTime) {
		this.sessionEndTime = exitTime;
	}

	// Getter and Setter for toteCount
	public Integer getToteCount() {
		return toteCount;
	}

	public void setToteCount(Integer toteCount) {
		this.toteCount = toteCount;
	}

	public Long getSessionID() {
		return this.sessionID;
	}

	public void setSessionID(Long sessionID) {
		this.sessionID = sessionID;
	}

	public String getScannerID() {
		return scannerID;
	}

	public void setScannerID(String scannerID) {
		this.scannerID = scannerID;
	}
}
