package com.kbs.model.db;

import java.sql.Timestamp;

public class FCZoneStationScannerMap {
	private Integer id;
	private String fcId;
	private String zoneId;
	private Integer stationId;
	private String scannerId;
	private Timestamp createdDate;
	private Boolean isActive;

	public FCZoneStationScannerMap() {

	}

	public FCZoneStationScannerMap(Integer id, String fcId, String zoneId, Integer stationId, String scannerId,
			Timestamp createdDate, Boolean isActive) {
		this.id = id;
		this.fcId = fcId;
		this.zoneId = zoneId;
		this.stationId = stationId;
		this.scannerId = scannerId;
		this.createdDate = createdDate;
		this.isActive = isActive;
	}

	public Integer getID() {
		return id;
	}

	public void setID(Integer id) {
		this.id = id;
	}

	public String getFcID() {
		return fcId;
	}

	public void setFcID(String fcId) {
		this.fcId = fcId;
	}

	public String getZoneID() {
		return zoneId;
	}

	public void setZoneID(String zoneId) {
		this.zoneId = zoneId;
	}

	public Integer getStationID() {
		return stationId;
	}

	public void setStationID(Integer stationId) {
		this.stationId = stationId;
	}

	public String getScannerID() {
		return scannerId;
	}

	public void setScannerID(String scannerId) {
		this.scannerId = scannerId;
	}

	public Timestamp getCreatedDate() {
		return createdDate;
	}

	public void setCreatedDate(Timestamp createdDate) {
		this.createdDate = createdDate;
	}

	public Boolean getIsActive() {
		return isActive;
	}

	public void setIsActive(Boolean isActive) {
		this.isActive = isActive;
	}
}
