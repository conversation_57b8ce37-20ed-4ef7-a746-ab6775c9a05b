package com.kbs.model;

public class ScannerToteDetails extends BaseClass {

	private String toteID;
	public Long sessionID;

	// Constructor
	public ScannerToteDetails() {
		super();
	}

	public ScannerToteDetails(String scannerID, String toteID, Long now, Long sessionID) {
		super(scannerID, now);
		this.toteID = toteID;
		this.sessionID = sessionID;
	}

	// Getters and Setters

	public String getToteID() {
		return toteID;
	}

	public void setToteID(String toteId) {
		this.toteID = toteId;
	}

	public Long getSessionID() {
		return sessionID;
	}

	public void setSessionID(Long sessionID) {
		this.sessionID = sessionID;
	}

    public Long getSessionStartTime() {
        // TODO Auto-generated method stub
        throw new UnsupportedOperationException("Unimplemented method 'getSessionStartTime'");
    }

}
