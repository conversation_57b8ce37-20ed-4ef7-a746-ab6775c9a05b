package com.kbs.model;

public class InActiveSessionData extends BaseClass {
	private String toteID;
	private String euID;
	private Long exitTime;
	private Long toteScanTime;

	// Getters and Setters

	public InActiveSessionData() {
		super();
	}

	public InActiveSessionData(String scannerId, String euId, String toteId, Long entryTime, Long endTime,
			Long toteScanTime) {
		super(scannerId, entryTime);
		this.euID = euId;
		this.toteID = toteId;
		this.exitTime = endTime;
		this.toteScanTime = toteScanTime;
	}

	public String getToteID() {
		return toteID;
	}

	public void setToteID(String toteID) {
		this.toteID = toteID;
	}

	public String getAssociateID() {
		return euID;
	}

	public void setAssociateID(String euID) {
		this.euID = euID;
	}

	public Long getEndTime() {
		return this.exitTime;
	}

	public void setEndTime(Long endTime) {
		this.exitTime = endTime;
	}

	public void setToteScanTime(Long value) {
		this.toteScanTime = value;
	}

	public long getToteScanTime() {
		return this.toteScanTime;
	}
}
