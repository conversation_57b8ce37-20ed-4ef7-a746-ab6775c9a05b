package com.kbs.model;

public class AssociateSession {
	public String associateID;
	public String scannerID;
	public Long sessionID;
	public Long sessionStartTime;
	public Long lastActiveTime;
	public Integer toteCount;

	public AssociateSession() {

	}

	public AssociateSession(String euID, String scannerID, Long sessionID, Long sessionStartTime, Long lastActiveTime) {
		this.associateID = euID;
		this.scannerID = scannerID;
		this.sessionID = sessionID;
		this.sessionStartTime = sessionStartTime;
		this.lastActiveTime = lastActiveTime;
		this.toteCount = 0;
	}

	// set setter and getter
	public String getEUID() {
		return this.associateID;
	}

	public void setEUID(String euID) {
		this.associateID = euID;
	}

	public Long getSessionID() {
		return this.sessionID;
	}

	public void setSessionID1(Long sessionID) {
		this.sessionID = sessionID;
	}

	public void setSessionStartTime(Long sessionStartTime) {
		this.sessionStartTime = sessionStartTime;
	}

	public Long getSessionStartTime() {
		return this.sessionStartTime;
	}

	public void setLastActiveTime(Long lastActiveTime) {
		this.lastActiveTime = lastActiveTime;
	}

	public Long getLastActiveTime() {
		return this.lastActiveTime;
	}

	public Integer getToteCount() {
		return this.toteCount;
	}

	public void setToteCount(Integer count) {
		this.toteCount = count;
	}

	public String getScannerID() {
		return scannerID;
	}

	public void setScannerID(String scannerID) {
		this.scannerID = scannerID;
	}

}
