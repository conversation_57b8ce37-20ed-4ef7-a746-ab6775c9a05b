package com.kbs.model;

public class AssociateScanner extends BaseClass {
	private String euID;
	private String sessionID;
	private Long exitTime;

	// Constructor
	public AssociateScanner() {
		super();
	}

	public AssociateScanner(String associateID, String scannerID, Long entryTime, Long endTime) {
		super(scannerID, entryTime);
		this.euID = associateID;
		this.exitTime = endTime;
	}

	// Getters and Setters
	public String getAssociateID() {
		return euID;
	}

	public void setAssociateID(String associateID) {
		this.euID = associateID;
	}

	public Long getExitTime() {
		return this.exitTime;
	}

	public void setExitTime(Long endTime) {
		this.exitTime = endTime;
	}

	public String getSessionID() {
		return this.sessionID;
	}

	public void setSessionID(String sessionID) {
		this.sessionID = sessionID;
	}
}
