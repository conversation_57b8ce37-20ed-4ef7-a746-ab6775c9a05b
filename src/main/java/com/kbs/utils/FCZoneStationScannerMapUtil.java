package com.kbs.utils;

import java.util.List;
import java.util.Optional;

import com.kbs.model.db.FCZoneStationScannerMap;

public class FCZoneStationScannerMapUtil {

	public static Integer getIdByScannerId(List<FCZoneStationScannerMap> list, String scannerId) {
		Optional<FCZoneStationScannerMap> result = list.stream().filter(map -> map.getScannerID().equals(scannerId))
				.findFirst();

		// If found, return the ID; otherwise, return null.
		return result.map(FCZoneStationScannerMap::getID).orElse(null);
	}
}
