package com.kbs.utils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class AppConfig {

	public static final int ArchiveScannerDetailsBatchSize = 0;
    public static String Key_EUScanner = "table:usermachine";
	public static String Key_EUScannerToteID = "table:%s:%s";
	public static String Key_ArchiveScannerData = "ArchiveScannerToteData"; // this key is used for archive data
	public static String Key_ScannerData = "ScannerToteData:%s"; // Associate Tote details
	public static String Redis_Connection_String = "localhost";
	public static int Redis_Connection_Port = 6379;
	public static String Session_Formatter = "yyyyMMddHHmmssnnnnnnnnn";
	public static String DB_URL = "**************************************";
	public static String DB_UserName = "postgres";
	public static String DB_Password = "admin@123";
	public static String Redis_Delete_Item = "DeletedItem";
	public static Integer SessionTimeout = 300; // 300 seconds
	public static String key_EUSession = "EUSession:%s"; // Associate Session and tote Count
	public static String key_ArchiveEUSession = "ArchiveEUSession";
	public static String key_All_Stations = "allstations";
	public static String key_All_Associates = "allassociates";
	public static List<String> machineList = new ArrayList<>(
			Arrays.asList("SC00001", "SC00002", "SC00003", "SC00004", "SC00005"));
	public static int ArchiveSessionBatchSize;

	private AppConfig() {

	}

	public static String getKey_EUScanner() {
		return Key_EUScanner;
	}

	public static void setKey_EUScanner(String key_EUScanner) {
		Key_EUScanner = key_EUScanner;
	}

	public static String getKey_EUScannerToteID() {
		return Key_EUScannerToteID;
	}

	public static void setKey_EUScannerToteID(String key_EUScannerToteID) {
		Key_EUScannerToteID = key_EUScannerToteID;
	}

	public static String getKey_ArchiveScannerData() {
		return Key_ArchiveScannerData;
	}

	public static void setKey_ArchiveScannerData(String key_ArchiveScannerData) {
		Key_ArchiveScannerData = key_ArchiveScannerData;
	}

	public static String getKey_ScannerData() {
		return Key_ScannerData;
	}

	public static void setKey_ScannerData(String key_ScannerData) {
		Key_ScannerData = key_ScannerData;
	}

	public static String getRedis_Connection_String() {
		return Redis_Connection_String;
	}

	public static void setRedis_Connection_String(String redis_Connection_String) {
		Redis_Connection_String = redis_Connection_String;
	}

	public static int getRedis_Connection_Port() {
		return Redis_Connection_Port;
	}

	public static void setRedis_Connection_Port(int redis_Connection_Port) {
		Redis_Connection_Port = redis_Connection_Port;
	}

	public static String getSession_Formatter() {
		return Session_Formatter;
	}

	public static void setSession_Formatter(String session_Formatter) {
		Session_Formatter = session_Formatter;
	}

	public static String getDB_URL() {
		return DB_URL;
	}

	public static void setDB_URL(String dB_URL) {
		DB_URL = dB_URL;
	}

	public static String getDB_UserName() {
		return DB_UserName;
	}

	public static void setDB_UserName(String dB_UserName) {
		DB_UserName = dB_UserName;
	}

	public static String getDB_Password() {
		return DB_Password;
	}

	public static void setDB_Password(String dB_Password) {
		DB_Password = dB_Password;
	}

	public static String getRedis_Delete_Item() {
		return Redis_Delete_Item;
	}

	public static void setRedis_Delete_Item(String redis_Delete_Item) {
		Redis_Delete_Item = redis_Delete_Item;
	}

	public static Integer getSessionTimeout() {
		return SessionTimeout;
	}

	public static void setSessionTimeout(Integer sessionTimeout) {
		SessionTimeout = sessionTimeout;
	}

	public static String getKey_EUSession() {
		return key_EUSession;
	}

	public static void setKey_EUSession(String key_EUSession) {
		AppConfig.key_EUSession = key_EUSession;
	}

	public static String getKey_ArchiveEUSession() {
		return key_ArchiveEUSession;
	}

	public static void setKey_ArchiveEUSession(String key_ArchiveEUSession) {
		AppConfig.key_ArchiveEUSession = key_ArchiveEUSession;
	}

	public static String getKey_All_Stations() {
		return key_All_Stations;
	}

	public static void setKey_All_Stations(String key_All_Stations) {
		AppConfig.key_All_Stations = key_All_Stations;
	}

	public static String getKey_All_Associates() {
		return key_All_Associates;
	}

	public static void setKey_All_Associates(String key_All_Associates) {
		AppConfig.key_All_Associates = key_All_Associates;
	}

	public static List<String> getMachineList() {
		return machineList;
	}

	public static void setMachineList(List<String> machineList) {
		AppConfig.machineList = machineList;
	}
}
