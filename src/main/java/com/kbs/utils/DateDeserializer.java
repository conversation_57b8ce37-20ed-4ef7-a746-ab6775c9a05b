package com.kbs.utils;

import java.io.IOException;
import java.sql.Timestamp;
import java.util.Date;

import com.fasterxml.jackson.core.JacksonException;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.deser.std.StdDeserializer;

public class DateDeserializer extends StdDeserializer<Date> {
	public DateDeserializer() {
		this(null);
		// TODO Auto-generated constructor stub
	}

	DateDeserializer(Class<?> vc) {
		super(vc);
		// TODO Auto-generated constructor stub
	}

	@Override
	public Date deserialize(JsonParser p, DeserializationContext ctxt) throws IOException, JacksonException {

		try {
			Timestamp stamp = new Timestamp(Long.parseLong(p.getText()));
			return new Date(stamp.getTime());
		} catch (IOException e) {
			return null;
		}
	}
}
