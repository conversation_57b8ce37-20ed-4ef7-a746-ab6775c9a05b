package com.kbs;

import java.io.IOException;
import java.io.InputStream;
import java.util.Properties;

import com.kbs.service.ProcessAssociateService;
import com.kbs.utils.AppConfig;

// import com.kbs.ArchiveData;

public class ProcessAssociates {
	private static ProcessAssociateService associateService;

	public static void main(String[] args) {
		UpdateAppSetting();
		associateService = new ProcessAssociateService();
		associateService.FindAndAddNewAssociate();
	}

	public static void UpdateAppSetting() {
		Properties config = new Properties();
		try (InputStream input = ArchiveData.class.getClassLoader().getResourceAsStream("config.properties")) {
			if (input == null) {
				System.out.println("Configuration file not found!");
				return;
			}
			config.load(input);
			AppConfig.SessionTimeout = Integer.parseInt(config.getProperty("app.sessiontimeout"));
			AppConfig.Redis_Connection_String = config.getProperty("app.RedisConnectionString");
			AppConfig.Redis_Connection_Port = Integer.parseInt(config.getProperty("app.RedisConnectionPort"));
			AppConfig.DB_URL = config.getProperty("app.DbURL");
			AppConfig.DB_UserName = config.getProperty("app.DbUserName");
			AppConfig.DB_Password = config.getProperty("app.DbPassword");
		} catch (IOException e) {
			System.out.println("Error loading configuration file: " + e.getMessage());
		}
	}

}
