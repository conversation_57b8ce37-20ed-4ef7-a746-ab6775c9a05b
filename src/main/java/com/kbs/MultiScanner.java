package com.kbs;

import java.io.IOException;
import java.io.InputStream;
import java.util.Properties;
import java.util.Random;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

import javax.swing.DefaultListModel;

import com.kbs.functions.CommonFunction;
import com.kbs.service.EUScannerService;
import com.kbs.service.ScannerToteService;
import com.kbs.utils.AppConfig;
import com.kbs.utils.BarcodeType;

// Class representing the Scanner Device
class Barcode {
	private int value;

	// Getter for the value
	public int getValue() {
		return value;
	}

	// Setter for the value
	public void setValue(int value) {
		this.value = value;
	}
}

class ScannerDevice implements Runnable {

	private String machineID;
	private String euID;
	private String previousBarcode = "";
	public Barcode barcode;

	private Random random;

	public ScannerDevice(String deviceId, String euID) {
		this.machineID = deviceId;
		this.euID = euID;
		this.random = new Random();
	}

	public ScannerDevice(String deviceId) throws InterruptedException {
		//SdkOperation instance = SdkOperation.getInstance();
		barcode = new Barcode();
		this.machineID = deviceId;
		// instance.registerCallbacks(deviceId);
		Thread.sleep(10000);
		// instance.ConnectDevice(deviceId);
		Thread.sleep(10000);
		this.random = new Random();
	}

	public void setPreviousBarcode(String barcode) {
		this.previousBarcode = barcode;
	}

	public String getPreviousBarcode() {
		return this.previousBarcode;
	}
	// Method to simulate reading data from a scanner

	private String readFromScanner() {

		// Simulate scanning data by returning random string
		int randomData = 10000000 + random.nextInt(90000000);

		return "ab" + Long.toString(randomData);

	}

	private void saveData(String userBarcode) {
		// Check for Duplicate
		if (this.previousBarcode != userBarcode) {
			ScannerToteService machineBucketBL = new ScannerToteService();
			EUScannerService userMachineBL = new EUScannerService();

			BarcodeType barcodeType = CommonFunction.getBarcodeType(userBarcode);
			if (barcodeType == BarcodeType.TOTE) {
				machineBucketBL.addScannerTote(userBarcode, machineID);
			} else if (barcodeType == BarcodeType.ASSOCIATE) {
				this.euID = userBarcode;
				userMachineBL.addUserMachine(userBarcode, machineID);
			}
			this.setPreviousBarcode(userBarcode);
		}
	}

	@Override

	public void run() {
		Random random = new Random();

		int count = 0;
		// Continuous reading loop

		while (true) {// make it true if you want to run continuously

			try {

				// Simulate reading data from the scanner
				TimeUnit.SECONDS.sleep(2);
				// String data = ScannersData.getInstance().get(this.machineID);
				String data = "42";
				saveData(data);
				count = count + 1;
			} catch (InterruptedException e) {

				System.out.println(machineID + " thread interrupted.");

				break; // Break the loop if the thread is interrupted

			}
		}
	}
}

//Main class to manage scanner devices

public class MultiScanner {

	private static final int NUM_DEVICES = 20; // Number of scanner devices

	public static void main(String[] args) throws InterruptedException {

		// Updating the configurations
		// read the values from configurations file
		UpdateAppSetting();
		// Create an executor service to manage threads
		// configuring honey well dll's.
		// Operation instance = SdkOperation.getInstance();
		// instance.setInitSdk();
		Thread.sleep(10000);
		// instance.searchDevices();
		Thread.sleep(10000);
		/// DefaultListModel<String> scannerList = instance.getScannerList();
		DefaultListModel<String> scannerList = new DefaultListModel<>();
		ExecutorService executorService = Executors.newFixedThreadPool(NUM_DEVICES);

		// Create and start threads for each scanner device

		for (int i = 0; i < scannerList.size(); i++) {
			String scanner = scannerList.get(i);

			String deviceId = scanner;

			ScannerDevice device = new ScannerDevice(deviceId);

			executorService.submit(device); // Submit the task to the executor service

		}

		// Add a shutdown hook to handle program termination gracefully

		Runtime.getRuntime().addShutdownHook(new Thread(() -> {

			System.out.println("Shutting down scanner system...");

			executorService.shutdownNow(); // Shutdown all running threads

			try {

				if (!executorService.awaitTermination(365, TimeUnit.DAYS)) {

					System.out.println("Forcing shutdown...");
					// instance.setDeinitSdk();
					executorService.shutdownNow();

				}

			} catch (InterruptedException e) {

				executorService.shutdownNow();
				// instance.setDeinitSdk();

			}

			System.out.println("Scanner system stopped.");

		}));

		// The main thread can perform other tasks while scanners run

		// In this example, we simply keep it alive

		try {

			TimeUnit.SECONDS.sleep(1); // Keep the system alive for 10 minutes

		} catch (InterruptedException e) {

			e.printStackTrace();
			// instance.setDeinitSdk();

		}

	}

	public static void UpdateAppSetting() {
		Properties config = new Properties();
		try (InputStream input = ArchiveData.class.getClassLoader().getResourceAsStream("config.properties")) {
			if (input == null) {
				System.out.println("Configuration file not found!");
				return;
			}
			config.load(input);
			AppConfig.SessionTimeout = Integer.parseInt(config.getProperty("app.sessiontimeout"));
			AppConfig.Redis_Connection_String = config.getProperty("app.RedisConnectionString");
			AppConfig.Redis_Connection_Port = Integer.parseInt(config.getProperty("app.RedisConnectionPort"));
			AppConfig.DB_URL = config.getProperty("app.DbURL");
			AppConfig.DB_UserName = config.getProperty("app.DbUserName");
			AppConfig.DB_Password = config.getProperty("app.DbPassword");
		} catch (IOException e) {
			System.out.println("Error loading configuration file: " + e.getMessage());
		}
	}

}
