package com.kbs.redis;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;
import java.time.Instant;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.kbs.model.AssociateSession;
import com.kbs.model.ScannerToteDetails;
import com.kbs.utils.AppConfig;

import redis.clients.jedis.Jedis;

public class RedisHelper {
	private Jedis jedis;
	ObjectMapper mapper = new ObjectMapper();

	// Constructor: Connect to Redis
	public RedisHelper() {
		this.jedis = new Jedis(AppConfig.Redis_Connection_String, AppConfig.Redis_Connection_Port); // Connect to
		// local Redis
	}

	public <T> void saveData(T dataList, String key) {
		// Serialize the generic list to JSON
		String serializedData = "";
		try {
			serializedData = mapper.writeValueAsString(dataList);
		} catch (JsonProcessingException e) {
			e.printStackTrace();
		}

		// Save JSON data in Redis with a specific key
		jedis.set(key, serializedData);
	}

	public <T> void appendDataList(List<T> dataList, String key) {
		// Serialize the generic list to JSON
		String serializedData = "";
		try {
			serializedData = mapper.writeValueAsString(dataList);
		} catch (JsonProcessingException e) {
			e.printStackTrace();
		}

		// Save JSON data in Redis with a specific key
		jedis.rpush(key, serializedData);
	}

	public <T> void appendData(T data, String key) {
		// Serialize the generic list to JSON
		String serializedData = "";
		try {
			serializedData = mapper.writeValueAsString(data);
		} catch (JsonProcessingException e) {
			e.printStackTrace();
		}

		// Save JSON data in Redis with a specific key
		jedis.rpush(key, serializedData);
	}

	public <T> void deleteData(String key) {

		jedis.del(key);
	}

	public <T> List<T> getObjects(String key, Class<T[]> clazz) {

		// Retrieve the serialized JSON data from Redis
		String retrievedData = jedis.get(key);
		if (retrievedData != null) {
			List<T> deserializedData = new ArrayList<>();

			try {
				// Deserialize JSON data into an array of the specified type
				T[] dataArray = mapper.readValue(retrievedData, clazz);
				deserializedData = new ArrayList<>(Arrays.asList(dataArray));

			} catch (JsonProcessingException e) {
				e.printStackTrace();
			}
			System.out.println(deserializedData);
			return deserializedData;
		} else {
			return null; // Return null if no data is found
		}
	}

	public <T> T getValue(String key, Class<T> clazz) {

		// Retrieve the serialized JSON data from Redis
		String retrievedData = jedis.get(key);
		T value;
		if (retrievedData != null) {
			try {
				// Deserialize JSON data into an array of the specified type
				value = mapper.readValue(retrievedData, clazz);
				return value;

			} catch (JsonProcessingException e) {
				e.printStackTrace();
			}

		}
		return null;
	}

	public <T> List<T> getDataList(String key, Class<T> clazz) {

		// Retrieve the serialized JSON data from Redis

		List<String> retrievedData = jedis.lrange(key, 0, -1);
		if (retrievedData != null) {
			List<T> deserializedData = new ArrayList<>();

			try {
				for (String item : retrievedData) {
					T data = mapper.readValue(item, clazz);
					deserializedData.add(data);
				}

			} catch (JsonProcessingException e) {
				e.printStackTrace();
			}
			System.out.println(deserializedData);
			return deserializedData;
		} else {
			return null; // Return null if no data is found
		}
	}

	public <T> List<T> getLastNthDataList(String key, Class<T> clazz, Integer n) {

		// Retrieve the serialized JSON data from Redis

		List<String> retrievedData = jedis.lrange(key, -n, -1);
		if (retrievedData != null) {
			List<T> deserializedData = new ArrayList<>();

			try {
				for (String item : retrievedData) {
					T data = mapper.readValue(item, clazz);
					deserializedData.add(data);
				}

			} catch (JsonProcessingException e) {
				e.printStackTrace();
			}
			System.out.println(deserializedData);
			return deserializedData;
		} else {
			return null; // Return null if no data is found
		}
	}

	public void updateIndexItem(String key, int index, String value) {
		jedis.lset(key, index, value);
	}

	public void DeleteIndexItem(String key, int index, String value) {
		jedis.lrem(key, index, value);
	}

	public <T> List<T> RemoveAndGetFirstItemFromList(String key, Class<T[]> clazz) {

		// Retrieve the serialized JSON data from Redis
		String retrievedData = jedis.lpop(key);
		if (retrievedData != null) {
			List<T> deserializedData = new ArrayList<>();

			try {
				// Deserialize JSON data into an array of the specified type
				T[] dataArray = mapper.readValue(retrievedData, clazz);
				deserializedData = new ArrayList<>(Arrays.asList(dataArray));

			} catch (JsonProcessingException e) {
				e.printStackTrace();
			}
			System.out.println(deserializedData);
			return deserializedData;
		} else {
			return null; // Return null if no data is found
		}
	}

	public <T> T RemoveAndGetItem(String key, Class<T> clazz) {

		// Retrieve the serialized JSON data from Redis
		String retrievedData = jedis.lpop(key);
		if (retrievedData != null) {

			try {
				// Deserialize JSON data into an array of the specified type
				T result = mapper.readValue(retrievedData, clazz);
				return result;

			} catch (JsonProcessingException e) {
				e.printStackTrace();
			}

		}
		return null; // Return null if no data is found

	}

	public void MoveDataFromOnelistoAnotherList(String fromKey, String toKey) {
		while (jedis.llen(fromKey) > 0) {
			jedis.rpoplpush(fromKey, toKey);
		}
	}

	public <T> void saveValueWithExpiration(T dataList, String key) {
		String serializedData = "";
		try {
			serializedData = mapper.writeValueAsString(dataList);
		} catch (JsonProcessingException e) {
			e.printStackTrace();
		}

		// Save JSON data in Redis with a specific key
		jedis.set(key, serializedData);
		jedis.expire(key, AppConfig.SessionTimeout);
	}

    public <T> List<T> RemoveAndGetBatch(String key, Class<T> clazz, int batchSize) {
        List<String> retrievedData = jedis.lrange(key, 0, batchSize - 1); // Get the first 'batchSize' elements
        if (retrievedData != null && !retrievedData.isEmpty()) {
            List<T> deserializedData = new ArrayList<>();
            try {
                for (String item : retrievedData) {
                    T data = mapper.readValue(item, clazz);
                    deserializedData.add(data);
                }
                jedis.ltrim(key, batchSize, -1); // Remove the processed elements from the list
            } catch (JsonProcessingException e) {
                e.printStackTrace();
            }
            return deserializedData;
        }
        return null; // Return null if no data is found
    }

    public Map<String, Integer> getHourlyAggregatedBarcodes(String key, String associateID, String stationID) {
        Map<String, Integer> hourlyData = new TreeMap<>();
        List<String> retrievedData = jedis.lrange(key, 0, -1);

        if (retrievedData != null && !retrievedData.isEmpty()) {
            long currentTime = System.currentTimeMillis();
            long oneDayAgo = currentTime - (24 * 60 * 60 * 1000);
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("HH:00").withZone(ZoneId.systemDefault());

            for (String item : retrievedData) {
                try {
                    Map<String, Object> data = mapper.readValue(item, Map.class);
                    long timestamp = (long) data.get("timestamp");
                    String dataAssociateID = (String) data.get("associateID");
                    String dataStationID = (String) data.get("stationID");

                    // Apply filtering by associateID and stationID
                    if (timestamp >= oneDayAgo &&
                        (associateID == null || associateID.equals(dataAssociateID)) &&
                        (stationID == null || stationID.equals(dataStationID))) {
                        
                        String hourKey = formatter.format(Instant.ofEpochMilli(timestamp));
                        hourlyData.put(hourKey, hourlyData.getOrDefault(hourKey, 0) + 1);
                    }
                } catch (JsonProcessingException e) {
                    e.printStackTrace();
                }
            }
        }
        return hourlyData;
    }
}
