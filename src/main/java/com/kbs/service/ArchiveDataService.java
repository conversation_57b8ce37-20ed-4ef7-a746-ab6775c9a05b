package com.kbs.service;

import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

import com.kbs.dao.DAOHelper;
import com.kbs.functions.CommonFunction;
import com.kbs.model.AssociateSession;
import com.kbs.model.AssociateSessionDetails;
import com.kbs.model.ScannerToteDetails;
import com.kbs.model.db.AssociateSessionSummary;
import com.kbs.model.db.FCZoneStationScannerMap;
import com.kbs.model.db.Station;
import com.kbs.redis.RedisHelper;
import com.kbs.utils.AppConfig;
import com.kbs.utils.FCZoneStationScannerMapUtil;

public class ArchiveDataService {
	private static final Logger LOGGER = Logger.getLogger(ArchiveDataService.class.getName());
	private static final int MILLISECONDS_IN_SECOND = 1000;

	private RedisHelper redisHelper;
	private DAOHelper daoHelper;

	// Constructor
	public ArchiveDataService() {
		this.redisHelper = new RedisHelper();
		this.daoHelper = new DAOHelper();
	}

	// Archive inactive session data
	public void archiveInactiveSessions(Integer sessionTimeoutInSeconds) {
		if (sessionTimeoutInSeconds == null || sessionTimeoutInSeconds <= 0) {
			LOGGER.log(Level.WARNING, "Invalid session timeout value: {0}", sessionTimeoutInSeconds);
			return;
		}

		try {
			List<Station> stations = daoHelper.GetAllStations();
			for (Station station : stations) {
				String key = String.format(AppConfig.key_EUSession, station.getScannerMAC());
				AssociateSession session = redisHelper.getValue(key, AssociateSession.class);

				if (session != null && isSessionInactive(session, sessionTimeoutInSeconds)) {
					archiveSessionData(session, key, station.getScannerMAC());
				}
			}
		} catch (Exception e) {
			LOGGER.log(Level.SEVERE, "Error archiving inactive sessions", e);
		}
	}

	private boolean isSessionInactive(AssociateSession session, Integer sessionTimeoutInSeconds) {
		return (System.currentTimeMillis() - session.lastActiveTime) > (sessionTimeoutInSeconds * MILLISECONDS_IN_SECOND);
	}

	private void archiveSessionData(AssociateSession session, String sessionKey, String scannerMAC) {
		try {
			redisHelper.appendData(session, AppConfig.key_ArchiveEUSession);
			redisHelper.deleteData(sessionKey);

			// Archive old tote data
			redisHelper.MoveDataFromOnelistoAnotherList(
					String.format(AppConfig.Key_ScannerData, scannerMAC),
					AppConfig.Key_ArchiveScannerData);
		} catch (Exception e) {
			LOGGER.log(Level.WARNING, "Error archiving session data for key: " + sessionKey, e);
		}
	}

	// Process the archived data and save it into the database
	public void processArchivedData() {
		try {
			List<FCZoneStationScannerMap> mapDetails = daoHelper.getFCZoneStationScannerMaps();

			processArchivedSessions(mapDetails);
			processArchivedScannerDetails();
		} catch (Exception e) {
			LOGGER.log(Level.SEVERE, "Error processing archived data", e);
		}
	}

	private void processArchivedSessions(List<FCZoneStationScannerMap> mapDetails) {
		int batchSize = AppConfig.ArchiveSessionBatchSize; // Use configurable batch size
		while (true) {
			List<AssociateSession> sessions = redisHelper.RemoveAndGetBatch(AppConfig.key_ArchiveEUSession, AssociateSession.class, batchSize);
			if (sessions == null || sessions.isEmpty()) {
				break;
			}

			for (AssociateSession session : sessions) {
				try {
					AssociateSessionSummary summary = new AssociateSessionSummary();
					summary.setAssociateID(session.getEUID());
					summary.setEntryTime(CommonFunction.ConvertMillsToTimeStamp(session.getSessionStartTime()));
					summary.setExitTime(CommonFunction.ConvertMillsToTimeStamp(session.getLastActiveTime()));
					summary.setToteCount(session.getToteCount());
					summary.setSessionID(session.getSessionID());
					summary.setScannerID(session.getScannerID());
					summary.setFCZoneStationScannerMapID(
							FCZoneStationScannerMapUtil.getIdByScannerId(mapDetails, session.getScannerID()));

					daoHelper.SaveAssociateSummaryData(summary);
				} catch (Exception e) {
					LOGGER.log(Level.WARNING, "Error processing archived session: " + session.getSessionID(), e);
				}
			}
		}
	}

	private void processArchivedScannerDetails() {
		int batchSize = AppConfig.ArchiveScannerDetailsBatchSize; // Use configurable batch size
		while (true) {
			List<ScannerToteDetails> detailsList = redisHelper.RemoveAndGetBatch(AppConfig.Key_ArchiveScannerData, ScannerToteDetails.class, batchSize);
			if (detailsList == null || detailsList.isEmpty()) {
				break;
			}

			for (ScannerToteDetails sessionDetails : detailsList) {
				try {
					AssociateSessionDetails details = new AssociateSessionDetails();
					details.setSessionID(sessionDetails.getSessionID());
					details.setToteID(sessionDetails.getToteID());
					details.setEntryTime(CommonFunction.ConvertMillsToTimeStamp(sessionDetails.getSessionStartTime()));

					daoHelper.SaveAssociateSummaryDetail(details);
				} catch (Exception e) {
					LOGGER.log(Level.WARNING, "Error processing archived scanner details for session ID: "
							+ sessionDetails.getSessionID(), e);
				}
			}
		}
	}

    public void ArchiveData(Integer sessionTimeoutInSeconds) {
        if (sessionTimeoutInSeconds == null || sessionTimeoutInSeconds <= 0) {
            LOGGER.log(Level.WARNING, "Invalid session timeout value: {0}", sessionTimeoutInSeconds);
            return;
        }

        archiveInactiveSessions(sessionTimeoutInSeconds);
        processArchivedData();
    }
}
