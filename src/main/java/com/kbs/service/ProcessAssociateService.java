package com.kbs.service;

import java.util.List;

import com.kbs.dao.DAOHelper;
import com.kbs.model.AssociateSession;
import com.kbs.model.db.AssociateDetail;
import com.kbs.model.db.Station;
import com.kbs.redis.RedisHelper;
import com.kbs.utils.AppConfig;

public class ProcessAssociateService {
	private RedisHelper redisHelper;
	private DAOHelper daoHelper;

	public ProcessAssociateService() {
		this.redisHelper = new RedisHelper();
		this.daoHelper = new DAOHelper();
	}

	public void FindAndAddNewAssociate() {
		DAOHelper dbHelper = new DAOHelper();
		Integer index = 0;
		// get all scanner details from redis, if not found get from database
		List<Station> stations = redisHelper.getObjects(AppConfig.key_All_Stations, Station[].class);
		// get all associates from from redis, if not found get from database
		List<AssociateDetail> associates = redisHelper.getObjects(AppConfig.key_All_Associates,
				AssociateDetail[].class);

		if (stations == null) {
			stations = dbHelper.GetAllStations();
			redisHelper.saveValueWithExpiration(stations, AppConfig.key_All_Stations);
		}
		if (associates == null) {
			associates = dbHelper.GetAllAssociates();
			redisHelper.saveValueWithExpiration(associates, AppConfig.key_All_Associates);
		}
		// check if the associate exist in DB or not
		for (Station station : stations) {
			String key = String.format(AppConfig.key_EUSession, station.getScannerMAC());
			AssociateSession session = redisHelper.getValue(key, AssociateSession.class);

			try {
				if (session != null) {
					Boolean associateExist = false;
					for (AssociateDetail associate : associates) {
						if (associate.getAssociateID().equals(session.getEUID())) {
							associateExist = true;
							break;
						}
					}
					if (!associateExist)
						daoHelper.AddAssociate(session.getEUID());
				}

			} catch (Exception e) {
				e.printStackTrace();
			}

		}

	}
}
