package com.kbs.service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

import com.kbs.model.AssociateSession;
import com.kbs.model.ScannerToteDetails;
import com.kbs.redis.RedisHelper;
import com.kbs.utils.AppConfig;

public class ScannerToteService {

	DateTimeFormatter dtf = DateTimeFormatter.ofPattern("yyyy/MM/dd HH:mm:ss");
	LocalDateTime now = LocalDateTime.now();

	public List<ScannerToteDetails> scannerTotes;
	public RedisHelper redisHelper;

	// Constructor
	public ScannerToteService() {
		this.scannerTotes = new ArrayList<>();
		this.redisHelper = new RedisHelper();
	}

	/**
	 * Adds a MachineBucket to the list if it doesn't exist, and saves it to Redis.
	 * 
	 * @param toteId    the bucket's serial number (BucketId)
	 * @param scannerID the machine's serial number (MachineSSN)
	 */
	public void addScannerTote(String toteId, String scannerId) {

		String scannerDataKey = String.format(AppConfig.Key_ScannerData, scannerId);
		List<ScannerToteDetails> savedTotes = redisHelper.getLastNthDataList(scannerDataKey, ScannerToteDetails.class,
				5);
		boolean exists = savedTotes.stream().anyMatch(tote -> tote.getToteID().equals(toteId));
		if (!exists) {
			String key = String.format(AppConfig.key_EUSession, scannerId);
			Integer toteCount = 1;
			AssociateSession session = redisHelper.getValue(key, AssociateSession.class);
			if (session != null)
				toteCount = session.getToteCount() + 1;
			session.setToteCount(toteCount);
			session.setLastActiveTime(System.currentTimeMillis());
			redisHelper.saveData(session, key);

			ScannerToteDetails scannerTote = new ScannerToteDetails(scannerId, toteId, System.currentTimeMillis(),
					session.getSessionID());

			redisHelper.appendData(scannerTote, String.format(AppConfig.Key_ScannerData, scannerId));

		}
	}

}
