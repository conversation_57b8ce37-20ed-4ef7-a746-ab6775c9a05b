package com.kbs.service;

import java.util.ArrayList;
import java.util.List;

import com.kbs.functions.CommonFunction;
import com.kbs.model.AssociateScanner;
import com.kbs.model.AssociateSession;
import com.kbs.redis.RedisHelper;
import com.kbs.utils.AppConfig;

public class EUScannerService {
	private List<AssociateScanner> euScanners;
	private RedisHelper redisHelper;

	// Constructor
	public EUScannerService() {
		this.redisHelper = new RedisHelper();
		this.euScanners = new ArrayList<>();
	}

	/**
	 * This method checks if the machine and user exist, if not, then adds it.
	 *
	 * @param euID      User's ID
	 * @param scannerID Machine's ID
	 */
	public synchronized void addUserMachine(String euID, String scannerID) {
		String key = String.format(AppConfig.key_EUSession, scannerID);

		AssociateSession session = redisHelper.getValue(key, AssociateSession.class);

		// Check if the user and machine combination already exists
		try {
			if (session != null
					&& (session.getEUID() != euID || (System.currentTimeMillis() - session.lastActiveTime) > 120000)) {
				redisHelper.appendData(session, AppConfig.key_ArchiveEUSession);
				redisHelper.deleteData(key);
				StartNewSession(euID, scannerID, key);
				// archive old tote data
				redisHelper.MoveDataFromOnelistoAnotherList(String.format(AppConfig.Key_ScannerData, scannerID),
						AppConfig.Key_ArchiveScannerData);
			}

		} catch (Exception e) {
			e.printStackTrace();
		}

		try {
			if (session == null) {
				// Start New session
				StartNewSession(euID, scannerID, key);

			}
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	private void StartNewSession(String euID, String scannerID, String key) {
		Long sessionStartTime = System.currentTimeMillis();
		AssociateSession newSession = new AssociateSession(euID, scannerID, CommonFunction.generateSessionId(),
				sessionStartTime, sessionStartTime);
		redisHelper.saveData(newSession, key);
	}

	public List<AssociateSession> getAssociatePeformanceData() {
		List<AssociateSession> sessions = new ArrayList<AssociateSession>();
		String key = "";

		for (String scanner : AppConfig.machineList) {
			key = String.format(AppConfig.key_EUSession, scanner);
			AssociateSession data = redisHelper.getValue(key, AssociateSession.class);
			sessions.add(data);
			System.out.println(data.getToteCount());
		}
		System.out.println(sessions);
		return sessions;
	}

}
