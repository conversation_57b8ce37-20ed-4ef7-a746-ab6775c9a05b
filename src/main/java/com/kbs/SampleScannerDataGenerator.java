package com.kbs;

import java.io.IOException;
import java.io.InputStream;
import java.util.Properties;
import java.util.Random;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

import com.kbs.functions.CommonFunction;
import com.kbs.service.EUScannerService;
import com.kbs.service.ScannerToteService;
import com.kbs.utils.AppConfig;
import com.kbs.utils.BarcodeType;

// Class representing the Scanner Device

class ScannerDevice1 implements Runnable {

	private String machineID;
	private String euID;
	private String previousBarcode = "";

	private Random random;

	public ScannerDevice1(String deviceId, String euID) {

		this.machineID = deviceId;
		this.euID = euID;
		this.random = new Random();

	}

	public void setPreviousBarcode(String barcode) {
		this.previousBarcode = barcode;
	}

	public String getPreviousBarcode() {
		return this.previousBarcode;
	}
	// Method to simulate reading data from a scanner

	private String readFromScanner() {

		// Simulate scanning data by returning random string
		int randomData = 10000000 + random.nextInt(90000000);

		return "ab" + Long.toString(randomData);

	}

	private void saveData(String userBarcode) {
		// Check for Duplicate
		if (this.previousBarcode != userBarcode) {
			ScannerToteService machineBucketBL = new ScannerToteService();
			EUScannerService userMachineBL = new EUScannerService();

			BarcodeType barcodeType = CommonFunction.getBarcodeType(userBarcode);
			if (barcodeType == BarcodeType.TOTE) {
				machineBucketBL.addScannerTote(userBarcode, machineID);
			} else if (barcodeType == BarcodeType.ASSOCIATE) {
				userMachineBL.addUserMachine(userBarcode, machineID);
			}
			this.setPreviousBarcode(userBarcode);
		}
	}

	@Override

	public void run() {
		Random random = new Random();
		saveData(euID);
		int count = 0;
		// Continuous reading loop

		while (count < 10) {

			try {

				// Simulate reading data from the scanner
				TimeUnit.SECONDS.sleep(random.nextInt(30) + 10);
				String data = readFromScanner();
				saveData(data);

				// Simulate the delay between scans

				// TimeUnit.SECONDS.sleep(random.nextInt(81) + 10); // Sleep for 2 seconds
				// before reading again
				count = count + 1;
			} catch (InterruptedException e) {

				System.out.println(machineID + " thread interrupted.");

				break; // Break the loop if the thread is interrupted

			}
		}
	}
}

//Main class to manage scanner devices

public class SampleScannerDataGenerator {

	private static final int NUM_DEVICES = 20; // Number of scanner devices

	public static void main(String[] args) {

		// Update App Config
		// read the values from config file
		UpdateAppSetting();
		// Create an executor service to manage threads

		ExecutorService executorService = Executors.newFixedThreadPool(NUM_DEVICES);

		// Create and start threads for each scanner device
		Integer counter = 0;
		for (String scanner : AppConfig.machineList) {

			String deviceId = scanner;

			ScannerDevice1 device = new ScannerDevice1(deviceId, "EU" + (12345 + counter++));

			executorService.submit(device); // Submit the task to the executor service

		}

		// Add a shutdown hook to handle program termination gracefully

		Runtime.getRuntime().addShutdownHook(new Thread(() -> {

			System.out.println("Shutting down scanner system...");

			executorService.shutdownNow(); // Shutdown all running threads

			try {

				if (!executorService.awaitTermination(3, TimeUnit.SECONDS)) {

					System.out.println("Forcing shutdown...");

					executorService.shutdownNow();

				}

			} catch (InterruptedException e) {

				executorService.shutdownNow();

			}

			System.out.println("Scanner system stopped.");

		}));

		// The main thread can perform other tasks while scanners run

		// In this example, we simply keep it alive

		try {

			TimeUnit.MINUTES.sleep(1); // Keep the system alive for 10 minutes

		} catch (InterruptedException e) {

			e.printStackTrace();

		}

	}

	public static void UpdateAppSetting() {
		Properties config = new Properties();
		try (InputStream input = ArchiveData.class.getClassLoader().getResourceAsStream("config.properties")) {
			if (input == null) {
				System.out.println("Configuration file not found!");
				return;
			}
			config.load(input);
			AppConfig.SessionTimeout = Integer.parseInt(config.getProperty("app.sessiontimeout"));
			AppConfig.Redis_Connection_String = config.getProperty("app.RedisConnectionString");
			AppConfig.Redis_Connection_Port = Integer.parseInt(config.getProperty("app.RedisConnectionPort"));
			AppConfig.DB_URL = config.getProperty("app.DbURL");
			AppConfig.DB_UserName = config.getProperty("app.DbUserName");
			AppConfig.DB_Password = config.getProperty("app.DbPassword");
		} catch (IOException e) {
			System.out.println("Error loading configuration file: " + e.getMessage());
		}
	}

}
