package com.kbs.dao;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.sql.Timestamp;

import com.kbs.utils.AppConfig;

public class AssociateSessionDetailsDAO {

	// Constructor to initialize the database connection
	public AssociateSessionDetailsDAO() {

	}

	// Method to insert a record into the AssociateSessionDetails table
	public void SaveAssociateSessionDetails(Long sessionID, String toteID, Timestamp toteScanTime) {
		String query = "INSERT INTO AssociateSessionDetail(SessionID, toteID, toteScanTime) " + "VALUES (?, ?, ?)";
		try (Connection conn = DriverManager.getConnection(AppConfig.DB_URL, AppConfig.DB_UserName,
				AppConfig.DB_Password); PreparedStatement stmt = conn.prepareStatement(query)) {

			stmt.setLong(1, sessionID);
			stmt.setString(2, toteID);
			stmt.setTimestamp(3, toteScanTime);

			int rowsInserted = stmt.executeUpdate();
			if (rowsInserted > 0) {
				System.out.println("A new session was inserted successfully!");
			}
		} catch (SQLException e) {
			e.printStackTrace();
		}
	}
}
