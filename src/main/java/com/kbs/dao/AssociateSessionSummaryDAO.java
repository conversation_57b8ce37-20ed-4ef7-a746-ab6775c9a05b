package com.kbs.dao;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.sql.Timestamp;

import com.kbs.utils.AppConfig;

public class AssociateSessionSummaryDAO {

	// Method to insert a record into AssociateSessionSummary table
	public void insertSession(String euid, String scannerID, Long sessionID, Timestamp entryTime, Timestamp exitTime,
			Integer toteCount, Integer fcZoneZtationZcannerMapID) {
		String sql = "INSERT INTO AssociateSessionSummary (EUID,ScannerID, SessionID , SessionStartTime, SessionEndTime, ToteCount,FCZoneStationScannerMapid) VALUES (?, ?, ?, ?, ?, ?, ?)";

		try (Connection conn = DriverManager.getConnection(AppConfig.DB_URL, AppConfig.DB_UserName,
				AppConfig.DB_Password); PreparedStatement stmt = conn.prepareStatement(sql)) {

			stmt.setString(1, euid);
			stmt.setString(2, scannerID);
			stmt.setLong(3, sessionID);
			stmt.setTimestamp(4, entryTime);
			stmt.setTimestamp(5, exitTime);
			stmt.setInt(6, toteCount);
			stmt.setInt(7, fcZoneZtationZcannerMapID);

			int rowsInserted = stmt.executeUpdate();
			if (rowsInserted > 0) {
				System.out.println("A new session was inserted successfully!");
			}
		} catch (SQLException e) {
			e.printStackTrace();
		}
	}

}
