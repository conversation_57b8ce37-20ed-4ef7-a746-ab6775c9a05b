package com.kbs.dao;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

import com.kbs.model.AssociateSessionDetails;
import com.kbs.model.db.AssociateDetail;
import com.kbs.model.db.AssociateSessionSummary;
import com.kbs.model.db.FCZoneStationScannerMap;
import com.kbs.model.db.Station;
import com.kbs.utils.AppConfig;

public class DAOHelper {
	private static final Logger LOGGER = Logger.getLogger(DAOHelper.class.getName());
	private static final String DB_URL = "**************************************";
	private static final String DB_USER = "postgres";
	private static final String DB_PASSWORD = "admin@123";

	private AssociateSessionSummaryDAO associateSessionSummaryDao;
	private AssociateSessionDetailsDAO associateSessionDetailsDao;

	public DAOHelper() {
		associateSessionSummaryDao = new AssociateSessionSummaryDAO();
		associateSessionDetailsDao = new AssociateSessionDetailsDAO();
	}

	public Connection getConnection() {
		try {
			return DriverManager.getConnection(DB_URL, DB_USER, DB_PASSWORD);
		} catch (SQLException e) {
			LOGGER.log(Level.SEVERE, "Failed to connect to the database. Please check the connection settings.", e);
			return null;
		}
	}

	public void SaveAssociateSummaryData(AssociateSessionSummary summary) {
		associateSessionSummaryDao.insertSession(summary.getAssociateID(), summary.getScannerID(),
				summary.getSessionID(), summary.getEntryTime(), summary.getExitTime(), summary.getToteCount(),
				summary.getFCZoneStationScannerMapID());
	}

	public void SaveAssociateSummaryDetail(AssociateSessionDetails details) {
		associateSessionDetailsDao.SaveAssociateSessionDetails(details.getSessionID(), details.getToteID(),
				details.getEntryTime());
	}

	public List<FCZoneStationScannerMap> getFCZoneStationScannerMaps() {
		String sql = "SELECT * FROM FCZoneStationScannerMap where IsActive = true";
		List<FCZoneStationScannerMap> details = new ArrayList<>();

		try (Connection conn = DriverManager.getConnection(AppConfig.DB_URL, AppConfig.DB_UserName,
				AppConfig.DB_Password); PreparedStatement stmt = conn.prepareStatement(sql)) {

			ResultSet rs = stmt.executeQuery();

			while (rs.next()) {
				Integer id = rs.getInt("ID");
				String fcId = rs.getString("FCID");
				String zoneId = rs.getString("ZONEID");
				Integer stationId = rs.getInt("STATIONID");
				String scannerId = rs.getString("SCANNERID");
				java.sql.Timestamp createdDate = rs.getTimestamp("EntryTime");
				Boolean isActive = rs.getBoolean("IsActive");

				// Create an object to hold the session data
				FCZoneStationScannerMap detail = new FCZoneStationScannerMap(id, fcId, zoneId, stationId, scannerId,
						createdDate, isActive);
				details.add(detail);
			}
		} catch (SQLException e) {
			e.printStackTrace();
		}
		return details;
	}

	public List<Station> GetAllStations() {
		String sql = "select * from Station where IsActive= true";
		List<Station> stations = new ArrayList<>();
		Connection connection = null;
		try {
			connection = getConnection();
			if (connection == null) {
				throw new SQLException("Database connection is null.");
			}
			try (PreparedStatement pstmt = connection.prepareStatement(sql)) {

				// Execute the query and process the results
				try (ResultSet rs = pstmt.executeQuery()) {
					while (rs.next()) {
						Station station = new Station();
						station.setId(rs.getInt("id"));
						station.setName(rs.getString("name"));
						station.setScannerMAC(rs.getString("scannermac"));
						station.setIsActive(rs.getBoolean("isactive"));
						stations.add(station);
					}
				}
			}
		} catch (SQLException e) {
			LOGGER.log(Level.SEVERE, "Error fetching stations from the database.", e);
			return null;
		} finally {
			if (connection != null) {
				try {
					connection.close();
				} catch (SQLException e) {
					LOGGER.log(Level.WARNING, "Error closing database connection.", e);
				}
			}
		}
		return stations;
	}

	public List<AssociateDetail> GetAllAssociates() {
		String sql = "SELECT EUID, EUFullName FROM associatedetail where IsActive = true";
		List<AssociateDetail> associates = new ArrayList<>();
		try (Connection conn = DriverManager.getConnection(AppConfig.DB_URL, AppConfig.DB_UserName,
				AppConfig.DB_Password); PreparedStatement pstmt = conn.prepareStatement(sql)) {

			// Execute the query and process the results
			try (ResultSet rs = pstmt.executeQuery()) {
				while (rs.next()) {
					AssociateDetail associate = new AssociateDetail();
					associate.setAssociateID(rs.getString("EUID"));
					associates.add(associate);
				}
			}

		} catch (SQLException e) {
			e.printStackTrace();
		}
		return associates;

	}

	public void AddAssociate(String euid) {
		String sql = "select * from  add_associate_if_not_exists(?)";

		try (Connection conn = DriverManager.getConnection(AppConfig.DB_URL, AppConfig.DB_UserName,
				AppConfig.DB_Password); PreparedStatement pstmt = conn.prepareStatement(sql)) {
			// Set the input parameter
			pstmt.setString(1, euid);
			// Execute the function
			pstmt.execute();
			System.out.println("Function executed successfully for euid: " + euid);
		} catch (SQLException e) {
			System.err.println("Error executing the function: " + e.getMessage());
		}
	}

}
