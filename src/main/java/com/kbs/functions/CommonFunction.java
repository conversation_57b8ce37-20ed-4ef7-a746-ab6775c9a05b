package com.kbs.functions;

import java.sql.Timestamp;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.UUID;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import com.kbs.utils.BarcodeType;

public class CommonFunction {

	// Regular expression pattern to match three letters followed by four digits
	public static String userBarcodepattern = "^([A-Za-z]{2}\\d{5}|[A-Za-z]{3}\\d{4})$";
	public static String toteBarcodepattern = "(^[A-Za-z]{2}\\d{8}$)|(^\\d{11})$";

	// Static method to get BarcodeType equivalent
	public static BarcodeType getBarcodeType(String barCode) {

		if (ValidateBarcodeCode(barCode, toteBarcodepattern)) {
			return BarcodeType.TOTE;
		} else if (ValidateBarcodeCode(barCode, userBarcodepattern)) {
			return BarcodeType.ASSOCIATE;
		}
		return null;
	}

	// Static method to check if the fullName is a Person's fullName
	public static boolean ValidateBarcodeCode(String input, String pattern) {

		// Using Pattern and Matcher to check the regex
		Pattern regex = Pattern.compile(pattern);
		Matcher matcher = regex.matcher(input);

		return matcher.matches();
	}

	public static Long generateSessionId() {
		UUID uuid = UUID.randomUUID();
		return uuid.getMostSignificantBits() & Long.MAX_VALUE;
		// return
		// Long.valueOf(LocalDateTime.now().format(DateTimeFormatter.ofPattern(AppConstant.Session_Formatter)));
	}

	public static LocalDateTime ConvertMillsToLocalTimeStamp(Long value) {
		return LocalDateTime.ofInstant(Instant.ofEpochMilli(value), ZoneId.systemDefault());
	}

	public static Timestamp ConvertMillsToTimeStamp(Long value) {
		return Timestamp.valueOf(LocalDateTime.ofInstant(Instant.ofEpochMilli(value), ZoneId.systemDefault()));
	}

}
