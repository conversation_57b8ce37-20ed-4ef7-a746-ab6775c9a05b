package com.kbs.controller;

import com.kbs.redis.RedisHelper;
import java.util.Map;

@RestController
public class BarcodeController {

    private final RedisHelper redisHelper;

    public BarcodeController() {
        this.redisHelper = new RedisHelper();
    }

    @GetMapping("/api/hourly-barcodes")
    public Map<String, Integer> getHourlyBarcodes(@RequestParam String key) {
        return redisHelper.getHourlyAggregatedBarcodes(key, key, key);
    }
}
