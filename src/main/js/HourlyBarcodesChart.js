import React, { useEffect, useState } from 'react';
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';

const HourlyBarcodesChart = ({ fetchHourlyData }) => {
    const [data, setData] = useState([]);

    useEffect(() => {
        const fetchData = async () => {
            const hourlyData = await fetchHourlyData();
            const formattedData = Object.entries(hourlyData).map(([hour, count]) => ({
                hour,
                count,
            }));
            setData(formattedData);
        };

        fetchData();
    }, [fetchHourlyData]);

    return (
        <ResponsiveContainer width="100%" height={400}>
            <BarChart data={data}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="hour" />
                <YAxis />
                <Tooltip />
                <Bar dataKey="count" fill="#8884d8" />
            </BarChart>
        </ResponsiveContainer>
    );
};

export default HourlyBarcodesChart;
