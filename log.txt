---sdkOperation init---
---before sdk init---
---after sdk init---
---sdkOperation init---
---before sdk init---
---after sdk init---
---sdkOperation init---
---before sdk init---
---after sdk init---
---find dev cb---
---find dev cb---
---sdkOperation init---
---before sdk init---
---after sdk init---
---sdkOperation init---
---before sdk init---
---after sdk init---
---sdkOperation init---
---before sdk init---
---after sdk init---
---find dev cb---
---find dev cb---
---sdkOperation register cb---
---sdkOperation register cb---
---sdkOperation connect SN: 23016B3437---
---sdkOperation Connect cb: true, SN: 23016B3437---
---sdkOperation register cb---
---sdkOperation register cb---
---sdkOperation connect SN: 23018B3C7B---
---sdkOperation Connect cb: true, SN: 23018B3C7B---
---get decode cb: 23016B3437, decode: 840030700323---
---get decode cb: 23018B3C7B, decode: 036000214000---
---get decode cb: 23018B3C7B, decode: VAO7867---
---get decode cb: 23018B3C7B, decode: 18947263829---
---get decode cb: 23018B3C7B, decode: 89692716291---
---get decode cb: 23018B3C7B, decode: 036000214000---
---get decode cb: 23018B3C7B, decode: VAO7867---
---get decode cb: 23018B3C7B, decode: VAO7867---
---get decode cb: 23018B3C7B, decode: 24920482031---
---get decode cb: 23018B3C7B, decode: 89692716291---
---get decode cb: 23018B3C7B, decode: 18947263829---
---get decode cb: 23018B3C7B, decode: 18947263829---
---get decode cb: 23018B3C7B, decode: 18947263829---
---get decode cb: 23018B3C7B, decode: 89692716291---
---sdkOperation init---
---before sdk init---
---after sdk init---
---find dev cb---
---find dev cb---
---sdkOperation register cb---
---sdkOperation register cb---
---sdkOperation connect SN: 23016B3437---
---sdkOperation Connect cb: true, SN: 23016B3437---
---sdkOperation register cb---
---sdkOperation register cb---
---sdkOperation connect SN: 23018B3C7B---
---sdkOperation Connect cb: true, SN: 23018B3C7B---
---get decode cb: 23018B3C7B, decode: VAO7867---
---get decode cb: 23018B3C7B, decode: VAO7867---
---get decode cb: 23018B3C7B, decode: VAO7867---
---get decode cb: 23018B3C7B, decode: 89692716291---
---get decode cb: 23018B3C7B, decode: 89692716291---
---get decode cb: 23018B3C7B, decode: 89692716291---
---get decode cb: 23018B3C7B, decode: 18947263829---
---get decode cb: 23018B3C7B, decode: 24920482031---
---get decode cb: 23018B3C7B, decode: 24920482031---
---get decode cb: 23018B3C7B, decode: 18947263829---
---get decode cb: 23018B3C7B, decode: 89692716291---
---get decode cb: 23016B3437, decode: VAO7867---
---get decode cb: 23016B3437, decode: VAO7867---
---get decode cb: 23016B3437, decode: VAO7867---
---get decode cb: 23016B3437, decode: VAO7867---
---get decode cb: 23016B3437, decode: 89692716291---
---get decode cb: 23016B3437, decode: VAO7867---
---get decode cb: 23018B3C7B, decode: 24920482031---
---sdkOperation init---
---before sdk init---
---after sdk init---
---find dev cb---
---find dev cb---
---sdkOperation register cb---
---sdkOperation register cb---
---sdkOperation connect SN: 23016B3437---
---sdkOperation Connect cb: true, SN: 23016B3437---
---sdkOperation register cb---
---sdkOperation register cb---
---sdkOperation connect SN: 23018B3C7B---
---sdkOperation Connect cb: true, SN: 23018B3C7B---
---get decode cb: 23016B3437, decode: VAO7867---
---get decode cb: 23016B3437, decode: VAO7867---
---get decode cb: 23016B3437, decode: VAO7867---
---get decode cb: 23016B3437, decode: VAO7867---
---get decode cb: 23016B3437, decode: VAO7867---
---get decode cb: 23016B3437, decode: VAO7867---
---get decode cb: 23016B3437, decode: VAO7867---
---get decode cb: 23016B3437, decode: VAO7867---
---get decode cb: 23018B3C7B, decode: VAO7867---
---get decode cb: 23018B3C7B, decode: VAO7867---
---get decode cb: 23018B3C7B, decode: VAO7867---
---sdkOperation init---
---before sdk init---
---after sdk init---
---find dev cb---
---find dev cb---
---sdkOperation register cb---
---sdkOperation register cb---
---sdkOperation connect SN: 23016B3437---
---sdkOperation Connect cb: true, SN: 23016B3437---
---sdkOperation register cb---
---sdkOperation register cb---
---sdkOperation connect SN: 23018B3C7B---
---sdkOperation Connect cb: true, SN: 23018B3C7B---
---get decode cb: 23018B3C7B, decode: VAO7867---
---get decode cb: 23018B3C7B, decode: VAO7867---
---get decode cb: 23018B3C7B, decode: VAO7867---
---get decode cb: 23018B3C7B, decode: VAO7867---
---get decode cb: 23018B3C7B, decode: VAO7867---
---get decode cb: 23018B3C7B, decode: VAO7867---
---get decode cb: 23018B3C7B, decode: VAO7867---
---get decode cb: 23018B3C7B, decode: VAO7867---
---get decode cb: 23018B3C7B, decode: 89692716291---
---get decode cb: 23018B3C7B, decode: 89692716291---
---sdkOperation init---
---before sdk init---
---after sdk init---
---find dev cb---
---find dev cb---
---sdkOperation register cb---
---sdkOperation register cb---
---sdkOperation connect SN: 23016B3437---
---sdkOperation Connect cb: true, SN: 23016B3437---
---sdkOperation register cb---
---sdkOperation register cb---
---sdkOperation connect SN: 23018B3C7B---
---sdkOperation Connect cb: true, SN: 23018B3C7B---
---get decode cb: 23018B3C7B, decode: VAO7867---
---get decode cb: 23018B3C7B, decode: VAO7867---
---get decode cb: 23018B3C7B, decode: 89692716291---
---get decode cb: 23018B3C7B, decode: 18947263829---
---get decode cb: 23018B3C7B, decode: 24920482031---
---sdkOperation init---
---before sdk init---
---after sdk init---
---sdkOperation init---
---before sdk init---
---after sdk init---
---sdkOperation init---
---before sdk init---
---after sdk init---
---sdkOperation init---
---before sdk init---
---after sdk init---
---find dev cb---
---sdkOperation register cb---
---sdkOperation register cb---
---sdkOperation connect SN: 23016B3437---
---sdkOperation Connect cb: false, SN: 23016B3437---
---sdkOperation Connect cb: false, SN: 23016B3437---
---sdkOperation init---
---before sdk init---
---after sdk init---
---find dev cb---
---sdkOperation register cb---
---sdkOperation register cb---
---sdkOperation connect SN: 23016B3437---
---sdkOperation Connect cb: false, SN: 23016B3437---
---sdkOperation Connect cb: false, SN: 23016B3437---
---sdkOperation init---
---before sdk init---
---after sdk init---
---find dev cb---
---sdkOperation register cb---
---sdkOperation register cb---
---sdkOperation connect SN: 23016B3437---
---sdkOperation Connect cb: true, SN: 23016B3437---
---get decode cb: 23016B3437, decode: EU12345---
---get decode cb: 23016B3437, decode: EU12345---
---get decode cb: 23016B3437, decode: EU12345---
---get decode cb: 23016B3437, decode: EU12345---
---get decode cb: 23016B3437, decode: EU12345---
---get decode cb: 23016B3437, decode: ab74522778---
---get decode cb: 23016B3437, decode: ab74522778---
---get decode cb: 23016B3437, decode: ab74522778---
---get decode cb: 23016B3437, decode: EU12345---
---get decode cb: 23016B3437, decode: ab74522778---
---get decode cb: 23016B3437, decode: ab74522778---
---get decode cb: 23016B3437, decode: ab74522778---
---sdkOperation Connect cb: false, SN: 23016B3437---
---sdkOperation init---
---before sdk init---
---after sdk init---
---sdkOperation init---
---before sdk init---
---after sdk init---
---find dev cb---
---sdkOperation register cb---
---sdkOperation register cb---
---sdkOperation connect SN: 23016B3437---
---sdkOperation Connect cb: true, SN: 23016B3437---
---get decode cb: 23016B3437, decode: EU12345---
---get decode cb: 23016B3437, decode: ab74522778---
---get decode cb: 23016B3437, decode: EU12345---
---get decode cb: 23016B3437, decode: ab74522778---
---get decode cb: 23016B3437, decode: VAO6548---
---get decode cb: 23016B3437, decode: ab74522778---
---get decode cb: 23016B3437, decode: ab74522782---
---get decode cb: 23016B3437, decode: ab74522781---
---get decode cb: 23016B3437, decode: ab74522781---
---sdkOperation init---
---before sdk init---
---after sdk init---
---sdkOperation init---
---before sdk init---
---after sdk init---
---find dev cb---
---sdkOperation register cb---
---sdkOperation register cb---
---sdkOperation connect SN: 23016B3437---
---sdkOperation Connect cb: true, SN: 23016B3437---
---get decode cb: 23016B3437, decode: VAO6548---
---get decode cb: 23016B3437, decode: ab74522782---
---get decode cb: 23016B3437, decode: VAO6548---
---get decode cb: 23016B3437, decode: ab74522781---
---get decode cb: 23016B3437, decode: EU12345---
---get decode cb: 23016B3437, decode: ab74522781---
---sdkOperation init---
---before sdk init---
---after sdk init---
---sdkOperation init---
---before sdk init---
---after sdk init---
